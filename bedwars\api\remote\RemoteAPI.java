package de.marcely.bedwars.api.remote;

import de.marcely.bedwars.api.BedwarsAPI;
import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.arena.Team;
import de.marcely.bedwars.api.event.remote.RemoteCustomMessageReceiveEvent;
import de.marcely.bedwars.api.game.spectator.SpectateReason;
import de.marcely.bedwars.api.unsafe.BedwarsAPILayer;
import org.bukkit.OfflinePlayer;
import org.bukkit.plugin.Plugin;
import org.jetbrains.annotations.Nullable;

import java.util.Collection;
import java.util.UUID;
import java.util.function.Consumer;

/**
 * Generally, this API only gets replaced by the ProxySync addon.
 * If it is not, because it for instance not installed, a general-use implementation will be used.
 * In that case, it will for instance only return all arenas of the local server.
 * This makes adding support for both solutions very easy, as you can just use this API if you plan to add support for remote servers,
 * or just {@link BedwarsAPI#getRemoteAPI()} if you don't.
 */
public interface RemoteAPI {

  /**
   * Returns whether a networking system has been actually implemented.
   * <p>
   *     Generally, this method returns <code>false</code>, meaning that you might as well just use the generic-use API.
   *     Plugins (such as the Enhanced ProxySync) addon may replace the RemoteAPI with their own. In this case, they should return <code>true</code>.
   * </p>
   *
   * @return <code>true</code> when it's only working with the local information
   */
  boolean isLocalOnly();

  /**
   * Returns whether the current instance is actually the instance that is currently implemented.
   * <p>
   *     In case this method returns <code>false</code>, the instance returned with {@link BedwarsAPI#getRemoteAPI()} differs from this one.
   * </p>
   *
   * @return <code>true</code> when you may continue using this object
   */
  default boolean isAPIActive() {
    return BedwarsAPI.getRemoteAPI() == this;
  }

  /**
   * Returns the plugin that has implemented the current {@link RemoteAPI} instance.
   * <p>
   *     By default, it will output the same as {@link BedwarsAPI#getPlugin()}.
   * </p>
   *
   * @return The plugin that has implemented the remote system
   */
  Plugin getImplementingPlugin();

  /**
   * Returns known arenas.
   *
   * @param includeLocal <code>true</code>: all arenas, <code>false</code>: only the ones from the other servers
   * @return A collection of arenas
   */
  Collection<? extends RemoteArena> getArenas(boolean includeLocal);

  /**
   * Returns all existing arenas.
   * <p>
   *     This includes the ones on this servers and the ones on the other servers.
   * </p>
   *
   * @return All (known) arenas
   */
  default Collection<? extends RemoteArena> getArenas() {
    return getArenas(true);
  }

  /**
   * Looks for an arena with that name and optionally parses it as an arena picker.
   *
   * @param name The name of the arena
   * @return The arena instance. Returns <code>null</code> if it hasn't found it
   */
  @Nullable
  RemoteArena getArenaByName(String name);

  /**
   * Looks for an arena with exactly that name.
   * <p>
   * 	Ignores display name, arena pickers etc.
   * </p>
   *
   * @param name The name of the arena
   * @return The arena instance. Returns <code>null</code> if it hasn't found it
   */
  @Nullable
  RemoteArena getArenaByExactName(String name);

  /**
   * Looks for an arena with that player inside the arena.
   * <p>
   * Ignores spectators and the state of the arena,
   * meaning it doesn't care whether the arena is currently in lobby, running or in endlobby.
   * </p>
   *
   * @param uuid The uuid of the player who joined the arena
   * @return The arena instance. Returns <code>null</code> when the player isn't inside the arena
   */
  @Nullable
  RemoteArena getArenaByPlayingPlayer(UUID uuid);

  /**
   * Looks for an arena with that player inside the arena.
   * <p>
   * Ignores spectators and the state of the arena,
   * meaning it doesn't care whether the arena is currently in lobby, running or in endlobby.
   * </p>
   *
   * @param player The player who joined the arena
   * @return The arena instance. Returns <code>null</code> when the player isn't inside the arena
   */
  @Nullable
  default RemoteArena getArenaByPlayingPlayer(RemotePlayer player) {
    return getArenaByPlayingPlayer(player.getUniqueId());
  }

  /**
   * Looks for an arena with that player inside the arena.
   * <p>
   * Ignores spectators and the state of the arena,
   * meaning it doesn't care whether the arena is currently in lobby, running or in endlobby.
   * </p>
   *
   * @param player The player who joined the arena
   * @return The arena instance. Returns <code>null</code> when the player isn't inside the arena
   */
  @Nullable
  default RemoteArena getArenaByPlayingPlayer(OfflinePlayer player) {
    return getArenaByPlayingPlayer(player.getUniqueId());
  }

  /**
   * Looks for an arena with that player spectating the arena.
   * <p>
   * 	Ignores normal players and the state of the arena,
   * 	meaning it doesn't care whether the arena is currently in lobby, running or in endlobby.
   * </p>
   *
   * @param uuid The uuid of the player who's spectating the arena
   * @return The arena instance. Returns null when the player isn't spectating any arena
   */
  @Nullable
  RemoteArena getArenaBySpectator(UUID uuid);

  /**
   * Looks for an arena with that player spectating the arena.
   * <p>
   * 	Ignores normal players and the state of the arena,
   * 	meaning it doesn't care whether the arena is currently in lobby, running or in endlobby.
   * </p>
   *
   * @param player The player who's spectating the arena
   * @return The arena instance. Returns null when the player isn't spectating any arena
   */
  @Nullable
  default RemoteArena getArenaBySpectator(RemotePlayer player) {
    return getArenaBySpectator(player.getUniqueId());
  }

  /**
   * Looks for an arena with that player spectating the arena.
   * <p>
   * 	Ignores normal players and the state of the arena,
   * 	meaning it doesn't care whether the arena is currently in lobby, running or in endlobby.
   * </p>
   *
   * @param player The player who's spectating the arena
   * @return The arena instance. Returns null when the player isn't spectating any arena
   */
  @Nullable
  default RemoteArena getArenaBySpectator(OfflinePlayer player) {
    return getArenaBySpectator(player.getUniqueId());
  }

  /**
   * Returns a collection of <b>all</b> known online players on the network.
   *
   * @return All players on the network
   */
  Collection<? extends RemotePlayer> getOnlinePlayers();

  /**
   * Returns the information of a player that is currently online on the network.
   *
   * @param uuid The uuid of the player who's currently on the network
   * @return The info object. May be <code>null</code> when the player isn't online
   */
  @Nullable
  RemotePlayer getOnlinePlayer(UUID uuid);

  /**
   * Returns the information of a player that is currently online on the network.
   *
   * @param player The player who's currently on the network
   * @return The info object. May be <code>null</code> when the player isn't online
   */
  @Nullable
  RemotePlayer getOnlinePlayer(OfflinePlayer player);

  /**
   * Returns the information of a player that is currently online on the network.
   *
   * @param name The username of the player
   * @return The info object. May be <code>null</code> when there's no player with the given name
   */
  @Nullable
  RemotePlayer getOnlinePlayer(String name);

  /**
   * Returns a collection of all known servers (including the local one {@link #getLocalServer()}).
   *
   * @return All known servers on the network
   */
  Collection<? extends RemoteServer> getServers();

  /**
   * Returns the local server.
   *
   * @return The local server
   */
  RemoteServer getLocalServer();

  /**
   * Makes the teleport himself into the arena. He will not actually play in it!
   * <p>
   *     Note: The reason it is located in {@link RemoteAPI} is to add an integration layer for the plugin implementing the network layer.<br>
   *     Alternative methods:<br>
   *     - {@link RemoteArena#teleportHere(RemotePlayer)}<br>
   *     - {@link RemoteArena#teleportHere(RemotePlayer, boolean)}<br>
   *     - {@link RemoteArena#teleportHere(RemotePlayer, Consumer)}<br>
   *     - {@link RemoteArena#teleportHere(RemotePlayer, boolean, Consumer)}
   * </p>
   *
   * @param player The player that shall get teleported
   * @param arena The arena in which he gets teleported into
   * @param sendMessage Whether a message shall be sent to the player when it failed or was succesful
   * @param callback A callback you may use to find out whether it was successful. May be <code>null</code>
   */
  void teleportPlayerToArena(RemotePlayer player, RemoteArena arena, boolean sendMessage, @Nullable Consumer<Boolean> callback);

  /**
   * Makes a player enter an arena as a spectator.
   * <p>
   *     Note: The reason it is located in {@link RemoteAPI} is to add an integration layer for the plugin implementing the network layer.<br>
   *     Alternative methods:<br>
   *     - {@link RemoteArena#addSpectator(RemotePlayer)}<br>
   *     - {@link RemoteArena#addSpectator(RemotePlayer, boolean)}<br>
   *     - {@link RemoteArena#addSpectator(RemotePlayer, Consumer)}<br>
   *     - {@link RemoteArena#addSpectator(RemotePlayer, boolean, Consumer)}<br>
   *     - {@link RemoteArena#addSpectator(RemotePlayer, SpectateReason)}<br>
   *     - {@link RemoteArena#addSpectator(RemotePlayer, SpectateReason, boolean)}<br>
   *     - {@link RemoteArena#addSpectator(RemotePlayer, SpectateReason, Consumer)}<br>
   *     - {@link RemoteArena#addSpectator(RemotePlayer, SpectateReason, boolean, Consumer)}
   * </p>
   *
   * @param player The player who shall enter as a spectator
   * @param arena The arena he shall enter
   * @param reason The reason he's spectating
   * @param ignoreArenaState <code>true</code>: Will always join the arena, even when the arena is not running
   * @param callback The callback you may use to find out whether it was successful. May be <code>null</code>
   */
  void addSpectatorToArena(RemotePlayer player, RemoteArena arena, SpectateReason reason, boolean ignoreArenaState, @Nullable Consumer<RemoteSpectatorAddResult> callback);

  /**
   * Makes the player enter the arena and play in it.
   * <p>
   *     Note: The reason it is located in {@link RemoteAPI} is to add an integration layer for the plugin implementing the network layer.<br>
   *     Alternative methods:<br>
   *     - {@link RemoteArena#addPlayer(RemotePlayer)}<br>
   *     - {@link RemoteArena#addPlayer(RemotePlayer, Team)}<br>
   *     - {@link RemoteArena#addPlayer(RemotePlayer, Consumer)}<br>
   *     - {@link RemoteArena#addPlayer(RemotePlayer, Team, Consumer)}<br>
   *     - {@link RemoteArena#addPlayer(AddRemotePlayerInfo, Consumer)}
   * </p>
   *
   * @param info Contains all the info, such as the players who is joining the arena
   * @param arena The arena they/he shall join
   * @param callback The callback you may use to find out whether it was successful. May be <code>null</code>
   */
  void addPlayerToArena(AddRemotePlayerInfo info, RemoteArena arena, @Nullable Consumer<RemotePlayerAddResult> callback);

  /**
   * Causes a player to get teleported to the hub server.
   * <p>
   *     It's possible that nothing will happen when he's already on a hub server.
   * </p>
   *
   * @param player The player who shall get moved
   */
  void sendToHub(RemotePlayer player);

  /**
   * Returns whether <i>sync-player-data</i> is active in ProxySync's configs.
   * <p>
   *   Player data refers to its stats, properties and achievements.
   * </p>
   * <p>
   *   As MBedwars by default doesn't support immediate player data syncing,
   *   this method will return <code>false</code> in case the ProxySync addon is not installed.
   * </p>
   *
   * @return Whether the current RemoteAPI implementation supports instant player data syncing
   */
  boolean isInstantPlayerDataSyncingActive();

  /**
   * Sends a custom message to any server on the network (apart from the local one).
   *
   * <p>
   *   This method is useful when you want to communicate between servers.
   *   Use {@link RemoteCustomMessageReceiveEvent} to listen to the message.
   * </p>
   * <p>
   *   Note that the message may not be received if the connection to other servers hasn't be established yet.
   *   You may use the #size() method of {@link #getServers()} to identify whether there are other servers on the network.
   * </p>
   * <p>
   *   The <code>channel</code> parameter is useful as it allows you to be distant from other plugins that make use of the messaging system
   *   as well. You may e.g. insert the name of your plugin. Note that encoded as UTF-8.
   *   You may later use {@link RemoteCustomMessageReceiveEvent#getChannel()} to identify the channel again.
   * </p>
   *
   * @param channel The channel in which you want to communicate
   * @param payload The actual message that you want to send. Use e.g. {@link String#getBytes()} to pass a String.
   * @see RemoteServer#sendCustomMessage(String, byte[])
   * @see RemoteCustomMessageReceiveEvent
   * @throws IllegalArgumentException When channel name is longer than 255 bytes or when payload is larger than 10MB (not a typo)
   */
  void broadcastCustomMessage(String channel, byte[] payload);

  /**
   * Transfers all queued-up player data to the other servers.
   * <p>
   *   In case {@link #isInstantPlayerDataSyncingActive()} is active, the ProxySync queues up the player data (properties, stats ...)
   *   and transfers it after a certain period. With this method, you may enforce it without the wait period.
   * </p>
   */
  void flushQueuedPlayerData();

  /**
   * Announce to all servers (including this one) that we would like to save all locally-cached player data.
   * <p>
   *   This is used for player ranks, as they must be saved before we catch the proper rank.
   * </p>
   * <p>
   *   The callback is always called on the main-thread.
   * </p>
   *
   * @param stats Whether to store stats
   * @param properties Whether to store properties
   * @param achievements Whether to store achievements
   * @param callback Gets called when the data has been saved
   */
  void saveAllPlayerData(boolean stats, boolean properties, boolean achievements, Runnable callback);

  /**
   * Returns the MBedwars API version that is implemented by the current RemoteAPI instance.
   * <p>
   *   Used internally to avoid compatibility issues.
   * </p>
   *
   * @return The implemented API version
   */
  int getImplementedAPIVersion();

  /**
   * Get the amount of players that have reserved a slot in the given arena.
   * <p>
   *   This is a part of a feature to make it e.g. easier for parties to join arenas.
   * </p>
   *
   * @param arena The arena for which you want to get the reservations count
   * @return The amount of players that have reserved a slot in the given arena
   * @see #hasReservation(UUID player, Arena arena)
   */
  int getReservationsCount(Arena arena);

  /**
   * Get whether a player has reserved a slot in the given arena.
   * <p>
   *   This is a part of a feature to make it e.g. easier for parties to join arenas.
   * </p>
   *
   * @param playerUUID The player for which you want to check the reservation
   * @param arena The arena for which you want to check the reservation
   * @return <code>true</code> when the player has reserved a slot in the given arena, <code>false</code> otherwise
   * @see #getReservationsCount(Arena arena)
   */
  boolean hasReservation(UUID playerUUID, Arena arena);

  /**
   * Returns the global RemoteAPI instance.
   *
   * @return The global RemoteAPI instance
   */
  static RemoteAPI get() {
    return BedwarsAPILayer.INSTANCE.getRemoteAPI();
  }
}
