/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.ChatColor
 *  org.bukkit.Sound
 *  org.bukkit.entity.FishHook
 *  org.bukkit.entity.LivingEntity
 *  org.bukkit.entity.Player
 *  org.bukkit.event.EventHandler
 *  org.bukkit.event.Listener
 *  org.bukkit.event.entity.ProjectileHitEvent
 *  org.bukkit.event.player.PlayerFishEvent
 *  org.bukkit.event.player.PlayerFishEvent$State
 *  org.bukkit.util.Vector
 */
package cn.acebrand.acefishrodknockback.event;

import cn.acebrand.acefishrodknockback.AceFishRodKnockback;
import de.marcely.bedwars.api.BedwarsAPI;
import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.arena.Team;
import org.bukkit.ChatColor;
import org.bukkit.Sound;
import org.bukkit.entity.FishHook;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.ProjectileHitEvent;
import org.bukkit.event.player.PlayerFishEvent;
import org.bukkit.util.Vector;

public class PlayerManager
        implements Listener {
    private final AceFishRodKnockback plugin;

    public PlayerManager(AceFishRodKnockback plugin) {
        this.plugin = plugin;
    }

    /**
     * 检测两个玩家是否为队友
     * 
     * @param player1 第一个玩家
     * @param player2 第二个玩家
     * @return 如果是队友返回true，否则返回false
     */
    private boolean areTeammates(Player player1, Player player2) {
        try {
            // 检查是否启用队友保护功能
            if (!this.plugin.getConfig().getBoolean("teammate-protection", true)) {
                return false;
            }

            // 获取第一个玩家所在的竞技场
            Arena arena = BedwarsAPI.getGameAPI().getArenaByPlayer(player1);
            if (arena == null) {
                return false; // 玩家不在任何竞技场中
            }

            // 检查第二个玩家是否在同一个竞技场中
            Arena arena2 = BedwarsAPI.getGameAPI().getArenaByPlayer(player2);
            if (arena2 == null || !arena.equals(arena2)) {
                return false; // 玩家不在同一个竞技场中
            }

            // 获取两个玩家的队伍
            Team team1 = arena.getPlayerTeam(player1);
            Team team2 = arena.getPlayerTeam(player2);

            // 检查是否都有队伍且队伍相同
            return team1 != null && team2 != null && team1.equals(team2);
        } catch (Exception e) {
            // 如果出现任何异常，为了安全起见返回false
            return false;
        }
    }

    @EventHandler
    public void onProjectileHit(ProjectileHitEvent event) {
        if (event.getEntity() instanceof FishHook && event.getHitEntity() instanceof Player) {
            FishHook hook = (FishHook) event.getEntity();
            Player hitPlayer = (Player) event.getHitEntity();
            Player shooter = null;
            if (hook.getShooter() instanceof Player) {
                shooter = (Player) hook.getShooter();
            }
            if (shooter != null && !shooter.equals((Object) hitPlayer)) {
                if (!this.plugin.getConfig().getBoolean("enabled", true)) {
                    return;
                }

                // 检查是否为队友，如果是队友则不进行击退和伤害
                if (areTeammates(shooter, hitPlayer)) {
                    return;
                }

                Vector direction = hitPlayer.getLocation().toVector().subtract(shooter.getLocation().toVector())
                        .normalize();
                direction.setY(0.5);
                double knockbackStrength = this.plugin.getConfig().getDouble("knockback-strength", 0.8);
                hitPlayer.setVelocity(direction.multiply(knockbackStrength));
                double damage = this.plugin.getConfig().getDouble("damage-per-hit", 0.3);
                double newHealth = Math.max(0.0, hitPlayer.getHealth() - damage);
                hitPlayer.setHealth(newHealth);
                hitPlayer.playSound(hitPlayer.getLocation(), Sound.ENTITY_PLAYER_HURT, 1.0f, 1.2f);
                shooter.playSound(shooter.getLocation(), Sound.ENTITY_PLAYER_HURT, 1.0f, 1.2f);
                if (this.plugin.getConfig().getBoolean("message-enabled", true)) {
                    String message = this.plugin.getConfig().getString("message",
                            "&c\u4f60\u88ab\u9c7c\u7aff\u51fb\u9000\u4e86\uff01");
                    hitPlayer.sendMessage(ChatColor.translateAlternateColorCodes((char) '&', (String) message));
                }
            }
        }
    }

    @EventHandler
    public void onPlayerFish(PlayerFishEvent event) {
        LivingEntity caught;
        FishHook hook = event.getHook();
        if (event.getState() == PlayerFishEvent.State.CAUGHT_ENTITY && hook.getHookedEntity() instanceof LivingEntity
                && (caught = (LivingEntity) hook.getHookedEntity()) instanceof Player) {

            Player caughtPlayer = (Player) caught;
            Player fisher = event.getPlayer();

            // 检查是否为队友，如果是队友则取消勾取效果
            if (areTeammates(fisher, caughtPlayer)) {
                hook.setHookedEntity(null);
                hook.remove();
                event.setCancelled(true);
                return;
            }

            // 对于非队友，保持原有逻辑：取消勾取效果（防止拉回）
            hook.setHookedEntity(null);
            hook.remove();
            event.setCancelled(true);
        }
    }
}
