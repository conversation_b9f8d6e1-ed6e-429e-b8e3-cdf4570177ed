/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.ChatColor
 *  org.bukkit.Sound
 *  org.bukkit.entity.FishHook
 *  org.bukkit.entity.LivingEntity
 *  org.bukkit.entity.Player
 *  org.bukkit.event.EventHandler
 *  org.bukkit.event.Listener
 *  org.bukkit.event.entity.ProjectileHitEvent
 *  org.bukkit.event.player.PlayerFishEvent
 *  org.bukkit.event.player.PlayerFishEvent$State
 *  org.bukkit.util.Vector
 */
package cn.acebrand.acefishrodknockback.event;

import cn.acebrand.acefishrodknockback.AceFishRodKnockback;
import org.bukkit.ChatColor;
import org.bukkit.Sound;
import org.bukkit.entity.FishHook;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.ProjectileHitEvent;
import org.bukkit.event.player.PlayerFishEvent;
import org.bukkit.util.Vector;

public class PlayerManager
implements Listener {
    private final AceFishRodKnockback plugin;

    public PlayerManager(AceFishRodKnockback plugin) {
        this.plugin = plugin;
    }

    @EventHandler
    public void onProjectileHit(ProjectileHitEvent event) {
        if (event.getEntity() instanceof FishHook && event.getHitEntity() instanceof Player) {
            FishHook hook = (FishHook)event.getEntity();
            Player hitPlayer = (Player)event.getHitEntity();
            Player shooter = null;
            if (hook.getShooter() instanceof Player) {
                shooter = (Player)hook.getShooter();
            }
            if (shooter != null && !shooter.equals((Object)hitPlayer)) {
                if (!this.plugin.getConfig().getBoolean("enabled", true)) {
                    return;
                }
                Vector direction = hitPlayer.getLocation().toVector().subtract(shooter.getLocation().toVector()).normalize();
                direction.setY(0.5);
                double knockbackStrength = this.plugin.getConfig().getDouble("knockback-strength", 0.8);
                hitPlayer.setVelocity(direction.multiply(knockbackStrength));
                double damage = this.plugin.getConfig().getDouble("damage-per-hit", 0.3);
                double newHealth = Math.max(0.0, hitPlayer.getHealth() - damage);
                hitPlayer.setHealth(newHealth);
                hitPlayer.playSound(hitPlayer.getLocation(), Sound.ENTITY_PLAYER_HURT, 1.0f, 1.2f);
                shooter.playSound(shooter.getLocation(), Sound.ENTITY_PLAYER_HURT, 1.0f, 1.2f);
                if (this.plugin.getConfig().getBoolean("message-enabled", true)) {
                    String message = this.plugin.getConfig().getString("message", "&c\u4f60\u88ab\u9c7c\u7aff\u51fb\u9000\u4e86\uff01");
                    hitPlayer.sendMessage(ChatColor.translateAlternateColorCodes((char)'&', (String)message));
                }
            }
        }
    }

    @EventHandler
    public void onPlayerFish(PlayerFishEvent event) {
        LivingEntity caught;
        FishHook hook = event.getHook();
        if (event.getState() == PlayerFishEvent.State.CAUGHT_ENTITY && hook.getHookedEntity() instanceof LivingEntity && (caught = (LivingEntity)hook.getHookedEntity()) instanceof Player) {
            hook.setHookedEntity(null);
            hook.remove();
            event.setCancelled(true);
        }
    }
}

