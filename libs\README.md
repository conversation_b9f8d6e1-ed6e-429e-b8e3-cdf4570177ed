# 本地依赖库目录

## 说明

此目录用于存放本地依赖的JAR文件，这些文件不在Maven中央仓库中提供。

## 需要的文件

请将以下文件放入此目录：

### MBedwars.jar
- **文件名**: `MBedwars.jar`
- **描述**: MBedwars插件的主JAR文件，包含API
- **获取方式**: 从MBedwars官方网站或服务器plugins目录获取
- **版本要求**: MBedwars 5.5.5版本，支持Minecraft 1.20.4

## 使用方法

1. 将 `MBedwars.jar` 文件复制到此目录
2. 确保文件名为 `MBedwars.jar`（区分大小写）
3. 运行 `mvn clean package` 进行编译

## 注意事项

- 请确保MBedwars.jar文件的版本与你的服务器版本兼容
- 此文件仅用于编译时依赖，运行时需要在服务器上安装MBedwars插件
- 不要将MBedwars.jar文件提交到版本控制系统中

## 目录结构

```
libs/
├── README.md          # 此说明文件
└── MBedwars.jar       # MBedwars插件JAR文件（需要手动添加）
```

## 编译命令

```bash
# 清理并编译
mvn clean compile

# 打包
mvn clean package

# 跳过测试打包
mvn clean package -DskipTests

# 安装到本地仓库
mvn clean install
```

## 故障排除

如果遇到编译错误：

1. **找不到MBedwars.jar**: 确保文件存在于libs目录中
2. **版本不兼容**: 检查MBedwars版本是否支持Minecraft 1.20.4
3. **权限问题**: 确保对libs目录有读取权限

## 替代方案

如果无法获取MBedwars.jar文件，可以：

1. 注释掉pom.xml中的MBedwars依赖
2. 修改相关代码以移除MBedwars API调用
3. 使用模拟对象进行开发和测试
