/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.ChatColor
 *  org.bukkit.command.Command
 *  org.bukkit.command.CommandSender
 *  org.bukkit.event.Listener
 *  org.bukkit.plugin.Plugin
 *  org.bukkit.plugin.java.JavaPlugin
 */
package cn.acebrand.acefishrodknockback;

import cn.acebrand.acefishrodknockback.event.PlayerManager;
import org.bukkit.ChatColor;
import org.bukkit.command.Command;
import org.bukkit.command.CommandSender;
import org.bukkit.event.Listener;
import org.bukkit.plugin.Plugin;
import org.bukkit.plugin.java.JavaPlugin;

public final class AceFishRodKnockback
extends JavaPlugin {
    private static AceFishRodKnockback instance;
    private PlayerManager playerManager;

    public void onEnable() {
        instance = this;
        this.saveDefaultConfig();
        this.playerManager = new PlayerManager(this);
        this.getServer().getPluginManager().registerEvents((Listener)this.playerManager, (Plugin)this);
        this.getLogger().info("AceFishRodKnockback \u63d2\u4ef6\u5df2\u542f\u7528\uff01");
    }

    public void onDisable() {
        this.getLogger().info("AceFishRodKnockback \u63d2\u4ef6\u5df2\u7981\u7528\uff01");
    }

    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (command.getName().equalsIgnoreCase("fishrod") && args.length > 0 && args[0].equalsIgnoreCase("reload")) {
            if (!sender.hasPermission("fishrod.reload")) {
                sender.sendMessage(String.valueOf(ChatColor.RED) + "\u4f60\u6ca1\u6709\u6743\u9650\u6267\u884c\u6b64\u547d\u4ee4\uff01");
                return true;
            }
            this.reloadConfig();
            sender.sendMessage(String.valueOf(ChatColor.GREEN) + "\u914d\u7f6e\u6587\u4ef6\u5df2\u91cd\u65b0\u52a0\u8f7d\uff01");
            sender.sendMessage(String.valueOf(ChatColor.YELLOW) + "\u51fb\u9000\u5f3a\u5ea6: " + this.getConfig().getDouble("knockback-strength", 0.8));
            sender.sendMessage(String.valueOf(ChatColor.YELLOW) + "\u4f24\u5bb3\u503c: " + this.getConfig().getDouble("damage-per-hit", 0.3));
            return true;
        }
        return false;
    }

    public static AceFishRodKnockback getInstance() {
        return instance;
    }
}

