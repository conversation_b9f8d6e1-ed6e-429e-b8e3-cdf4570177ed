package de.marcely.bedwars.api.event.player;

import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.event.arena.ArenaEvent;
import de.marcely.bedwars.api.game.specialitem.SpecialItem;
import de.marcely.bedwars.api.game.specialitem.SpecialItemUseSession;
import org.bukkit.entity.Player;
import org.bukkit.event.HandlerList;
import org.bukkit.event.player.PlayerEvent;

/**
 * Gets called when a special item is done doing its thing
 */
public class PlayerStopSpecialItemEvent extends PlayerEvent implements ArenaEvent {

  private static final HandlerList HANDLERS = new HandlerList();

  private final SpecialItem specialItem;
  private final Arena arena;
  private final SpecialItemUseSession session;

  public PlayerStopSpecialItemEvent(PlayerUseSpecialItemEvent event, SpecialItemUseSession session) {
    this(event.getPlayer(), event.getSpecialItem(), event.getArena(), session);
  }

  public PlayerStopSpecialItemEvent(Player player, SpecialItem specialItem, Arena arena, SpecialItemUseSession session) {
    super(player);

    this.specialItem = specialItem;
    this.arena = arena;
    this.session = session;
  }

  /**
   * Returns the special item that the player has used.
   *
   * @return The special item that has been used
   */
  public SpecialItem getSpecialItem() {
    return this.specialItem;
  }

  /**
   * Returns the arena in which the player has used the item.
   *
   * @return The arena involved in this
   */
  public Arena getArena() {
    return this.arena;
  }

  /**
   * Returns the session that was stopped
   *
   * @return The session that was stopped
   */
  public SpecialItemUseSession getSession() {
    return this.session;
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }
}
