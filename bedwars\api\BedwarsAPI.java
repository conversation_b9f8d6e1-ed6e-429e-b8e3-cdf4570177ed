package de.marcely.bedwars.api;

import de.marcely.bedwars.api.command.CommandsCollection;
import de.marcely.bedwars.api.configuration.ConfigurationAPI;
import de.marcely.bedwars.api.event.ConfigsLoadEvent;
import de.marcely.bedwars.api.hook.HookAPI;
import de.marcely.bedwars.api.message.MessageAPI;
import de.marcely.bedwars.api.player.PlayerDataAPI;
import de.marcely.bedwars.api.remote.RemoteAPI;
import de.marcely.bedwars.api.unsafe.BedwarsAPILayer;
import de.marcely.bedwars.api.world.WorldStorage;
import de.marcely.bedwars.api.arena.picker.ArenaPickerAPI;
import de.marcely.bedwars.tools.Helper;
import de.marcely.bedwars.tools.NMSHelper;
import org.bukkit.World;
import org.bukkit.command.CommandSender;
import org.bukkit.plugin.java.JavaPlugin;
import org.jetbrains.annotations.Nullable;

/**
 * Start here!
 * <p>
 * This API is build in a tree-like-structure.
 * Everything is ordered and you can access most of it by starting here.
 * Some classes, mainly enums, are still directly accessible.
 * </p>
 */
@SuppressWarnings("deprecation")
public class BedwarsAPI {

  private BedwarsAPI() {
  }

  /**
   * Returns the main class of the MBedwars plugin.
   *
   * @return The main class
   */
  public static JavaPlugin getPlugin() {
    return BedwarsAPILayer.INSTANCE.getPlugin();
  }

  /**
   * Attempts to reload the MBedwars plugin
   *  1. Kick all players from all running arenas
   *  2. Cancel any running migration processes
   *  3. Unload data, (Achievements, Stats, Player Properties, etc)
   *  4. Reload all MBedwars config files
   *
   * @param sender Who is trying to reload the plugin. Where reload progress messages will be sent
   * @return if the plugin was successfully able to reload
   */
  public static boolean reload(@Nullable CommandSender sender) {
    return BedwarsAPILayer.INSTANCE.reload(sender);
  }

  /**
   * Returns the current run state of the MBedwars plugin.
   *
   * @return The plugin state of MBedwars
   */
  public static PluginState getState() {
    return BedwarsAPILayer.INSTANCE.getState();
  }

  /**
   * Contains API for game related stuff, such as arenas, lobbies, spawners, special items etc.
   *
   * @return The global GameAPI instance
   */
  public static GameAPI getGameAPI() {
    return BedwarsAPILayer.INSTANCE.getGameAPI();
  }

  /**
   * Contains API that is relevant to arena pickers.
   *
   * @return The global ArenaPickerAPI instance
   */
  public static ArenaPickerAPI getArenaPickerAPI() {
    return BedwarsAPILayer.INSTANCE.getArenaPickerAPI();
  }

  /**
   * Contains API that is being used for communicating with other servers in a Proxy network.
   * <p>
   *     Generally, this API only gets replaced by the ProxySync addon.
   *     If it is not, because it for instance not installed, a general-use implementation will be used.
   *     In that case, it will for instance only return all arenas of the local server.
   *     This makes adding support for both solutions very easy, as you can just use this API if you plan to add support for remote servers,
   *     or just {@link #getGameAPI()} if you don't.
   * </p>
   *
   * @return The remote API. The API may change during the runtime
   */
  public static RemoteAPI getRemoteAPI() {
    return BedwarsAPILayer.INSTANCE.getRemoteAPI();
  }

  /**
   * Implement your own remote API.
   * <p>
   *     Generally, this API only gets replaced by the ProxySync addon.
   *     If it is not, because it for instance not installed, a general-use implementation will be used.
   *     In that case, it will for instance only return all arenas of the local server.
   *     This makes adding support for both solutions very easy, as you can just use this API if you plan to add support for remote servers,
   *     or just {@link #getGameAPI()} if you don't.
   * </p>
   *
   * @param remoteAPI The new remote API
   */
  public static void setRemoteAPI(RemoteAPI remoteAPI) {
    BedwarsAPILayer.INSTANCE.setRemoteAPI(remoteAPI);
  }

  /**
   *
   * Contains API for getting and managing player data, such as stats and achievements.
   *
   * @return The global PlayerDataAPI instance
   */
  public static PlayerDataAPI getPlayerDataAPI() {
    return BedwarsAPILayer.INSTANCE.getPlayerDataAPI();
  }

  /**
   * Contains API for managing the messaging system.
   *
   * @return The global MessageAPI instance
   */
  public static MessageAPI getMessageAPI() {
    return BedwarsAPILayer.INSTANCE.getMessageAPI();
  }

  /**
   * Contains API for handing addons.
   *
   * @return The global AddonAPI instance
   */
  public static AddonAPI getAddonAPI() {
    return BedwarsAPILayer.INSTANCE.getAddonAPI();
  }

  /**
   * Contains API for managing hooks.
   *
   * @return The global HookAPI instance
   */
  public static HookAPI getHookAPI() {
    return BedwarsAPILayer.INSTANCE.getHookAPI();
  }

  /**
   * Contains API for getting/setting configs, as well as saving config files.
   *
   * @return The global ConfigurationAPI instance
   */
  public static ConfigurationAPI getConfigurationAPI() {
    return BedwarsAPILayer.INSTANCE.getConfigurationAPI();
  }

  /**
   * Contains API/Helper to access NMS stuff.
   *
   * @return The global NMSHelper instance
   */
  public static NMSHelper getNMSHelper() {
    return BedwarsAPILayer.INSTANCE.getNMSHelper();
  }

  /**
   * Contains utility stuff.
   *
   * @return The global Helper instance
   */
  public static Helper getHelper() {
    return BedwarsAPILayer.INSTANCE.getHelper();
  }

  /**
   * Returns the commands collection that contains all sub commands under /bw.
   *
   * @return The very first commands collection
   */
  public static CommandsCollection getRootCommandsCollection() {
    return BedwarsAPILayer.INSTANCE.getRootCommandsCollection();
  }

  /**
   * A world storage contains anything needed for creating custom entities or blocks.
   * This may include a ranking statue or a holographic dealer.
   *
   * @param world There's an unique storage for every world. This parameter specifies which one we want
   * @return The storage corresponding to the given world. <code>null</code> if the world doesn't exist anymore
   */
  @Nullable
  public static WorldStorage getWorldStorage(World world) {
    return BedwarsAPILayer.INSTANCE.getWorldStorage(world);
  }

  /**
   * Initiate a process of migration from another system, such as a Bedwars plugin or storage system.
   * This doesn't effectively start the process, you must do that using {@link MigrationProcess#run()}.
   *
   * @return The process that has been initiated and is ready to be run
   */
  public static MigrationProcess prepareMigrationProcess(MigrationProcess.Origin origin) {
    return BedwarsAPILayer.INSTANCE.initMigrationProcess(origin);
  }

  /**
   * Returns all running migration processes.
   * Manually start one using {@link #prepareMigrationProcess(MigrationProcess.Origin)}.
   *
   * @return All running migration processes.
   */
  public static MigrationProcess[] getRunningMigrationProcesses() {
    return BedwarsAPILayer.INSTANCE.getRunningMigrationProcesses();
  }

  /**
   * Calls the callback when the API is ready to use.
   * <p>
   * This event can be called at any situation (given that MBedwars is even loaded) and it will always get an response.
   * <p>
   * We HIGHLY encourage you to use it as MBedwars can take some time until it's fully loaded.
   * Using {@link ConfigsLoadEvent} is not an alternative as it does not get called when your plugin is getting reloaded afterwards.
   *
   * @param runn The callback
   */
  public static void onReady(Runnable runn) {
    BedwarsAPILayer.INSTANCE.onReady(runn);
  }

  /**
   * Returns the current version of the API.
   * <p>
   * You can for instance use it during the enabling process of your plugin to identify if the latest supported version of MBedwars is installed.
   * <pre>{@code
   * try {
   * 	Class apiClass = Class.forName("de.marcely.bedwars.api.BedwarsAPI");
   * 	int apiVersion = (int) apiClass.getMethod("getAPIVersion").invoke(null);
   *
   * 	if (apiVersion < 100)
   * 		throw new IllegalStateException();
   *  } catch(Exception e) {
   *  	getLogger().warning("Sorry, your installed version of MBedwars is not supported. Please install at least v5.4");
   *   	Bukkit.getPluginManager().disable(this);
   *  	return;
   *  }
   *     }</pre>
   *
   *     Visit the following page to find a list with all api versions and their corresponding api version: <a href="https://s.marcely.de/mbww13">https://s.marcely.de/mbww13</a>
   *
   *
   * @return The API version of the currently running plugin
   */
  public static int getAPIVersion() {
    return 205;
  }
}