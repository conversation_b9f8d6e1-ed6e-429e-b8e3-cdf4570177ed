package de.marcely.bedwars.api.hook;

import org.bukkit.entity.Player;
import org.jetbrains.annotations.Nullable;

/**
 * Represents a hook that modifies the display name of a player.
 */
public interface NicknamingHook extends Hook {

  default HookCategory getCategory() {
    return HookCategory.NICKNAMING;
  }

  /**
   * Get whether a player currently has a nickname.
   *
   * @param player The player we want to check
   * @return <code>true</code> in case his display name has been modified
   */
  default boolean hasNickname(Player player) {
    return getNickname(player) != null;
  }

  /**
   * Get the nickname that a player is currently using.
   *
   * @param player The player we want to check
   * @return The nickname. <code>null</code> if he doesn't have any
   */
  @Nullable
  String getNickname(Player player);

  /**
   * Get the real name of the player.
   *
   * @param player The player we want to check
   * @return His real name. <code>null</code> if he doesn't have any nickname or it's just {@link org.bukkit.entity.Player#getName()}
   */
  @Nullable
  String getRealName(Player player);
}
