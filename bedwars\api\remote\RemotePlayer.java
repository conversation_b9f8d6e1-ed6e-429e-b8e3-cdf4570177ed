package de.marcely.bedwars.api.remote;

import de.marcely.bedwars.tools.Validate;
import java.time.Instant;
import net.md_5.bungee.api.chat.BaseComponent;
import org.bukkit.entity.Player;
import org.jetbrains.annotations.Nullable;

import java.util.UUID;

/**
 * Represents a player on the network.
 * <P>
 *     This includes the ones that are online on this server and the ones who are online on other ones.<br>
 *     Also keep in mind that this instance gets replaced with an other one whenever he changes servers,
 *     meaning that most of the methods won't work in that case. Use {@link #isOnline()} to check that.
 * </P>
 */
public interface RemotePlayer {

  /**
   * The name of the player.
   * <P>
   *     Exactly the same as {@link de.marcely.bedwars.tools.Helper#getNickedPlayerRealName(org.bukkit.entity.Player)}.
   * </P>
   *
   * @return His name
   */
  String getName();

  /**
   * The unique id of the player.
   *
   * <P>
   *     Exactly the same as {@link Player#getUniqueId()}.
   * </P>
   *
   * @return His uuid
   */
  UUID getUniqueId();

  /**
   * Sends messages to the player.
   *
   * @param messages The messages that shall be sent
   */
  void sendMessage(String... messages);

  /**
   * Sends a message to the player.
   *
   * @param components The components of the message that shall be sent
   */
  void sendMessage(BaseComponent... components);

  /**
   * Gets the server in which he's currently on.
   *
   * @return The server he is playing on
   */
  RemoteServer getServer();

  /**
   * Returns the arena that he is currently playing in or spectating.
   *
   * @return The arena he attends. <code>null</code> when he isn't
   */
  @Nullable RemoteArena getArena();

  /**
   * Returns whether he is playing a match (also includes the lobby state).
   *
   * @return <code>true</code> when he's a part of a match
   */
  boolean isPlaying();

  /**
   * Returns whether he is currently spectating an arena.
   *
   * @return <code>true</code> when he's spectating an arena
   */
  boolean isSpectating();

  /**
   * Gets whether he is currently online.
   * <p>
   *     Keep in mind that this instance gets replaced with an other one when he switches the servers.
   *     In this case, this method will return <code>false</code> for this one, while the new instance will return <code>true</code>.
   * </p>
   *
   * @return Whether he is currently online
   */
  boolean isOnline();

  /**
   * Gets whether the player is currently playing on this server and not on another one.
   *
   * @return <code>true</code> he is a player on this server
   */
  boolean isLocal();

  /**
   * Gets the Player instance in case he is playing on this server.
   *
   * @return Bukkit's player instance of this player. <code>null</code> when he's either not online or playing on another server.
   */
  @Nullable
  Player asBukkit();

  /**
   * Makes the player connect with another server.
   *
   * @param channelName The name of the server that has been configured in BungeeCord's configs
   */
  void connectTo(String channelName);

  /**
   * Makes the player connect with another server.
   *
   * @param server The target server
   */
  default void connectTo(RemoteServer server) {
    Validate.notNull(server, "server");

    connectTo(server.getBungeeChannelName());
  }

  /**
   * Causes a player to get teleported to the hub server.
   * <p>
   *     It's possible that nothing will happen when he's already on a hub server.
   * </p>
   */
  void sendToHub();

  /**
   * Get the time since when we know that the player has joined the given server (not the network!).
   *
   * @return The time when the player has joined the given server
   */
  Instant getLoginTime();
}
