package de.marcely.bedwars.tools.gui;

import de.marcely.bedwars.tools.Validate;
import de.marcely.bedwars.tools.gui.type.VillagerGUI;
import java.util.List;
import org.bukkit.inventory.ItemStack;
import org.jetbrains.annotations.Nullable;

import java.util.Random;
import java.util.UUID;

/**
 * Represents a buyable offer that can be added to a {@link VillagerGUI}
 */
public class VillagerOffer {

  private static final long ID_MOST_BIT = new Random().nextInt();
  private static long ID_INDEX = Long.MIN_VALUE;

  private final UUID id;

  private ItemStack result, price1, price2;

  public VillagerOffer(ItemStack result, ItemStack price1) {
    this(result, price1, null);
  }

  public VillagerOffer(ItemStack result, List<ItemStack> prices) {
    this(result, prices.get(0), prices.size() > 1 ? prices.get(1) : null);
  }

  public VillagerOffer(ItemStack result, ItemStack price1, @Nullable ItemStack price2) {
    Validate.notNull(result, "result");
    Validate.notNull(price1, "price1");

    this.id = new UUID(ID_MOST_BIT, ID_INDEX++);
    this.result = result.clone();
    this.price1 = price1.clone();
    this.price2 = price2 != null ? price2.clone() : null;
  }

  /**
   * Returns a random unique id that's bound to this offer
   *
   * @return The id of this offer
   */
  public UUID getId() {
    return this.id;
  }

  /**
   * Returns the item that will be given to the player once he purchased it
   *
   * @return The result of the offer
   */
  public ItemStack getResult() {
    return this.result.clone();
  }

  /**
   * Returns the first item that the player must have and give to purchase the offer
   *
   * @return The first price
   */
  public ItemStack getFirstPrice() {
    return this.price1.clone();
  }

  /**
   * Returns the optional second price that the player must have and give to purchase the offer
   *
   * @return The second price. <code>null</code> if {@link #hasSecondPrice()} returns false
   */
  public @Nullable ItemStack getSecondPrice() {
    return this.price2 != null ? this.price2.clone() : null;
  }

  /**
   * Returns if a second unique price is required for the player to purchase the item
   *
   * @return <code>true</code> if a second price is given
   */
  public boolean hasSecondPrice() {
    return this.price2 != null;
  }

  /**
   * Set the item that will be given to the player once the offer has been purchased
   *
   * @param result The new result item
   */
  public void setResult(ItemStack result) {
    Validate.notNull(result, "result");

    this.result = result.clone();
  }

  /**
   * Set the first price that will be taken from the player once he purchased the offer
   *
   * @param price1 The new first price
   */
  public void setFirstPrice(ItemStack price1) {
    Validate.notNull(price1, "price1");

    this.price1 = price1.clone();
  }

  /**
   * Set the new optional second price that will be taken from the player once he purchased the offer
   *
   * @param price2 The new second price. May be null if there shouldn't be a second one
   */
  public void setSecondPrice(@Nullable ItemStack price2) {
    this.price2 = price2 != null ? price2.clone() : null;
  }

  /**
   * Returns the {@link #getId()} that will be used for the next constructed offer
   *
   * @return The id of the next offer
   */
  public static UUID getNextId() {
    return new UUID(ID_MOST_BIT, ID_INDEX);
  }
}
