package de.marcely.bedwars.api.arena;

import de.marcely.bedwars.api.GameAPI;
import de.marcely.bedwars.api.event.arena.ArenaBedBreakEvent;
import de.marcely.bedwars.api.event.arena.ArenaDeleteEvent;
import de.marcely.bedwars.api.event.arena.ArenaIssuesCheckEvent;
import de.marcely.bedwars.api.event.player.PlayerOpenArenaChestEvent;
import de.marcely.bedwars.api.game.shop.BuyGroup;
import de.marcely.bedwars.api.game.spawner.DropType;
import de.marcely.bedwars.api.game.spawner.Spawner;
import de.marcely.bedwars.api.game.specialitem.SpecialItemUseSession;
import de.marcely.bedwars.api.game.spectator.KickSpectatorReason;
import de.marcely.bedwars.api.game.spectator.SpectateReason;
import de.marcely.bedwars.api.game.spectator.Spectator;
import de.marcely.bedwars.api.game.upgrade.TeamEnchantment;
import de.marcely.bedwars.api.game.upgrade.UpgradeState;
import de.marcely.bedwars.api.message.Message;
import de.marcely.bedwars.api.remote.RemoteAPI;
import de.marcely.bedwars.api.remote.RemoteArena;
import de.marcely.bedwars.api.world.hologram.HologramEntity;
import de.marcely.bedwars.tools.PersistentStorage;
import de.marcely.bedwars.tools.VarSound;
import de.marcely.bedwars.tools.location.IntXYZ;
import de.marcely.bedwars.tools.location.XYZ;
import de.marcely.bedwars.tools.location.XYZD;
import de.marcely.bedwars.tools.location.XYZYP;
import java.io.File;
import java.time.Duration;
import java.time.Instant;
import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.function.Consumer;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.WeatherType;
import org.bukkit.World;
import org.bukkit.block.Block;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.metadata.Metadatable;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.jetbrains.annotations.Nullable;

/**
 * An arena is a place where a match may happen and effectively the handler of a match.
 * There are multiple things that define an arena:<br>
 *  - The status ({@link #getStatus()}) describing what exactly the arena is doing at a given moment<br>
 *  - The type/regeneration type ({@link #getRegenerationType()}) the form of the arena<br>
 *  - Various properties, such as the name, authors, teams, spawners...<br>
 * Those are needed to have a working match.
 */
public interface Arena extends Metadatable, PersistentStorage.Holder {

  /**
   * Returns the unique name of the arena.
   *
   * @return Returns the actual name of the arena
   */
  String getName();

  /**
   * Changes the name of the arena.
   * <p>
   * Operation can fail when there's already an arena with the name or when the name is invalid.
   * Use {@link GameAPI#isArenaNameValid(String)} to check if the name is valid
   *
   * @param name The new name
   * @return Returns whether it was successful or not
   */
  boolean setName(String name);

  /**
   * Returns the non-unique name of the arena for displaying to players
   * <p>
   * Uses the custom name if one is configured, otherwise uses {@link #getName()}
   *
   * @return The name of the arena used in displays
   */
  String getDisplayName();

  /**
   * Returns whether this arena has a custom name enabled that'll be displayed ({@link #getDisplayName()}) in favor of the actual name ({@link #getName()}).
   *
   * @return <code>true</code> when the feature is being used with this arena
   */
  boolean isCustomNameEnabled();

  /**
   * Set whether this arena has a custom name enabled that'll be displayed ({@link #getDisplayName()}) in favor of the actual name ({@link #getName()}).
   *
   * @param customNameEnabled <code>true</code> causes this feature to get enabled for this arena
   */
  void setCustomNameEnabled(boolean customNameEnabled);

  /**
   * Returns the custom name of the arena.
   * <p>
   *     Keep this mind that this method always returns a non-null value, meaning that internally there's always one, even if {@link #isCustomNameEnabled()} returns false.
   * </p>
   *
   * @return The custom name.
   */
  String getCustomName();

  /**
   * Set the custom name of the arena.
   * <p>
   *     You might want to use {@link #setCustomNameEnabled(boolean)} as well.
   * </p>
   *
   * @param customName The new custom name.
   */
  void setCustomName(String customName);

  /**
   * Returns the current state of the arena
   *
   * @return The current state of the arena
   */
  ArenaStatus getStatus();

  /**
   * Not only changes the status, but also does operations depending on the status.
   * <p>
   * Examples:<br>
   * - Enables regeneration process with {@link ArenaStatus#RESETTING}<br>
   * - Starting the match with {@link ArenaStatus#RUNNING}<br>
   * It may NOT support this for all operations
   * </p>
   *
   * @param status The new status
   * @throws IllegalStateException When trying to set it to {@link ArenaStatus#RUNNING} and certain conditions (not in lobby, no players) aren't met
   */
  void setStatus(ArenaStatus status);

  /**
   * Returns the regeneration type aka the form/variant of the arena.
   *
   * @return The regeneration type of the arena
   */
  RegenerationType getRegenerationType();

  /**
   * This method allows you to change the form of the arena
   *
   * @param type The new regeneration type
   */
  void setRegenerationType(RegenerationType type);

  /**
   * This method calculates an approximate duration of the regeneration of an arena.
   * <p>
   * The returned value might be far away from the real time.
   * It might return {@link Double#NaN} when it's not possible to calculate it at the given moment.
   *
   * @return The estimated time in seconds of how long it'll take to regenerate the arena
   */
  double getApproxRegenerationTime();

  /**
   * Returns every team that has been enabled for this arena.
   *
   * @return All added teams
   */
  Set<Team> getEnabledTeams();

  /**
   * Enables or disables a team in this arena.
   * <p>
   * Disabling an arena can cause some data to disappear, such as the location of their beds.<br>
   * It's not possible to enable or disable an arena while {@link #getStatus()} returns anything other than {@link ArenaStatus#STOPPED}.
   *
   * @param team The team that shall be enabled or disabled
   * @param enabled The new state
   * @return If a change was done. Might return false when the new state is equal to the old state or some conditions aren't given
   */
  boolean setTeamEnabled(Team team, boolean enabled);

  /**
   * Returns whether or not a team is enabled.
   *
   * @param team The team we want to check
   * @return <code>true</code> if it's enabled
   */
  boolean isTeamEnabled(Team team);

  /**
   * Returns the location the team's bed location.
   * <p>
   * 	Might return null if it hasn't been set yet or when the team hasn't been added.
   * <p>
   * The world that's being used is {@link #getGameWorld()}.
   *
   * @param team The team that owns the bed
   * @return The location of the bed. Might return <code>null</code>
   * @see #getBedParts(Team)
   */
  @Nullable
  XYZD getBedLocation(Team team);

  /**
   * Returns the team that owns the bed part at the given location.
   *
   * @param block The block of a bed part
   * @return The team that owns the bed part. May be <code>null</code> if no team owns a bed at the given location
   */
  @Nullable
  Team getTeamByBedBlock(Block block);

  /**
   * Returns the team that owns the bed part at the given location.
   *
   * @param loc The location of a bed part
   * @return The team that owns the bed part. May be <code>null</code> if no team owns a bed at the given location
   */
  @Nullable
  Team getTeamByBedBlock(IntXYZ loc);

  /**
   * Returns the blocks of all the team's bed parts.
   * <p>
   *     A bed usually persists of two parts: the head and the body. The actually structure may vary depending on the bed material that is being used.
   *     This method returns the locations of all bed parts.
   * </p>
   * <p>
   *     Length might be 0 if no bed has been added.
   * </p>
   *
   * @param team The team that owns the bed
   * @return All the locations/blocks of the bed parts
   * @see #getBedLocation(Team)
   * @see #getBedPartsAsync(Team, java.util.function.Consumer)
   */
  Block[] getBedParts(Team team);

  /**
   * Returns the blocks of all the team's bed parts.
   * <p>
   * See {@link #getBedParts(Team)} The difference is that this method loads the chunks async,
   * while the other might freeze the main thread until they did. For peformance reasons,
   * you should use this async one instead - if possible.
   * </p>
   *
   * @param team The team that owns the bed
   * @param callback The callback that will be called when the blocks have been loaded
   * @see #getBedParts(Team)
   */
  void getBedPartsAsync(Team team, Consumer<Block[]> callback);

  /**
   * Sets the bed location of a team.
   * <p>
   * This won't automatically replace the bed when the game is running.<p>
   * The world that's being used is {@link #getGameWorld()}.<p>
   * The operation might fail when the given team hasn't been added to the arena.
   *
   * @param team The team that owns the bed
   * @param loc The location of the bed, <code>null</code> if it shall be removed in the storage
   * @return <code>true</code> if the operation was successful
   */
  boolean setBedLocation(Team team, @Nullable XYZD loc);

  /**
   * Returns whether the bed of a team has been destroyed.
   * <p>
   *   This only returns the internal state.
   *   The bed blocks might still be there.
   * </p>
   *
   * @param team The team that owns the bed
   * @return If it has been destroyed or not
   */
  boolean isBedDestroyed(Team team);

  /**
   * Get the time when the bed of a team has been destroyed.
   *
   * @param team The team that owns the bed
   * @return The time of when the bed has been destroyed. May be <code>null</code> if it hasn't been destroyed yet
   */
  @Nullable
  Instant getBedDestructionTime(Team team);

  /**
   * Gets the hologram that is optionally being spawned above the team's bed.
   * <p>
   *   Hologram may be used to e.g. display the state of the bed.
   *   May be <code>null</code> if e.g. the round hasn't started yet or it has been disabled.
   * </p>
   *
   * @param team The team of the bed
   * @return The hologram above the bed. May be <code>null</code>
   */
  @Nullable
  HologramEntity getBedHologram(Team team);

  /**
   * Gets the spawn point of a team.
   * Actual spawn point might vary as the location might be blocked by a block.
   * <p>
   * It's possible that this method returns <code>null</code> when the team hasn't been added yet or the spawn point hasn't been set.
   * <p>
   * The world that's being used is {@link #getGameWorld()}.
   *
   * @param team The team we want to obtain it from
   * @return The location of their spawn. Might return <code>null</code>
   */
  @Nullable XYZYP getTeamSpawn(Team team);

  /**
   * Set the spawn point of a team.
   * <p>
   * The world that's being used is {@link #getGameWorld()}.<p>
   * The operation might fail when the given team hasn't been added to the arena.
   *
   * @param team The team that will teleport at that location
   * @param loc The new location, use <code>null</code> to remove it from the storage
   * @return <code>true</code> when the operation was successful
   */
  boolean setTeamSpawn(Team team, @Nullable XYZYP loc);

  /**
   * Returns the UpgradeState of a specified team
   *
   * @param team The team we want to get the UpgradeState from
   * @return The UpgradeState for the team. May be <code>null</code> if the arena is not running
   */
  @Nullable UpgradeState getUpgradeState(Team team);

  /**
   * Returns effects applied to a team around their base
   *
   * @param team The team that we are getting the effects from
   * @return A list of potion effects that are being applied
   */
  List<PotionEffect> getTeamBaseOnlyEffects(Team team);

  /**
   * Returns all effects applied to a team everywhere
   *
   * @param team The team that we are getting the effects from
   * @return A list of potion effects that are being applied
   */
  List<PotionEffect> getTeamPermanentEffects(Team team);

  /**
   * Adds an effect for all the players in a certain team
   *
   * @param team The team the effect should be added to
   * @param baseOnly Whether the effect is only active inside a teams base
   * @param potionEffectType the effect that should be given
   * @param amplifier How strong the effect is
   */
  void addTeamEffect(Team team, boolean baseOnly, PotionEffectType potionEffectType, int amplifier);

  /**
   * Removes an effect from a team
   *
   * @param team The team we will try and remove the effect from
   * @param potionEffectType The effect type that should be removed
   * @return <code>true</code> when one equal instance has been found and removed
   */
  boolean removeTeamEffect(Team team, PotionEffectType potionEffectType);

  /**
   * Returns all team enchantments applied to a team
   *
   * @param team The team we want to obtain it from
   * @return A list of team enchantments the team owns
   */
  List<TeamEnchantment> getTeamEnchantments(Team team);

  /**
   * Adds a team enchantment to a team, and applies it to team players
   *
   * @param team The team that the new enchantment is for
   * @param teamEnchantment The new team enchantment being applied
   */
  void addTeamEnchantment(Team team, TeamEnchantment teamEnchantment);

  /**
   * Removes a team enchantment from a team, and applies it to team players
   *
   * @param team The team that we are removing the enchantment from
   * @param teamEnchantment The team enchantment that is being removed
   * @return <code>true</code> when one equal instance has been found and removed
   */
  boolean removeTeamEnchantment(Team team, TeamEnchantment teamEnchantment);

  /**
   * Tries to place the bed of a team.
   * <p>
   *  This method also modifies the internal state of the bed (basically revives the team).
   *  This operation is being done silently, meaning the players won't be notified about this.
   * </p>
   * <p>
   *   Bed blocks are being placed async, if the Spigot version supports it.
   *   This means that it is theoretically not possible to immediately check the block type immediately after calling this method.
   * </p>
   *
   * @param team The team that owns the bed
   * @see #destroyBed(Team)
   */
  void placeBed(Team team);

  /**
   * Tries to break/destroy the bed of a team.
   * <p>
   *  This method also modifies the internal state of the bed (basically causes the team to not being able to respawn).
   *  This operation is being done silently, meaning the players won't be notified about this.
   * </p>
   * <p>
   *   Bed blocks are being destroyed async, if the Spigot version supports it.
   *   This means that it is theoretically not possible to immediately check the block type immediately after calling this method.
   * </p>
   *
   * @param team The team that owns the bed
   * @see #placeBed(Team)
   */
  void destroyBed(Team team);

  /**
   * Tries to place the bed blocks of a team, but not change the internal state.
   * <p>
   *   Bed blocks are being placed async, if the Spigot version supports it.
   *   This means that it is theoretically not possible to immediately check the block type immediately after calling this method.
   * </p>
   *
   * @param team The team from whom the bed blocks shall be destroyed
   * @see #destroyBedBlocks(Team)
   */
  void placeBedBlocks(Team team);

  /**
   * Tries to break/destroy the bed blocks of a team, but not change the internal state.
   * <p>
   *   Bed blocks are being destroyed async, if the Spigot version supports it.
   *   This means that it is theoretically not possible to immediately check the block type immediately after calling this method.
   * </p>
   *
   * @param team The team from whom the bed blocks shall be destroyed
   * @see #placeBedBlocks(Team)
   */
  void destroyBedBlocks(Team team);

  /**
   * Will destroy the bed as if a player actually broke it ingame.
   *
   * @param team The team that owns the bed
   * @param player The player who broke it. May be <code>null</code> if its unknown
   * @return The event containing the result. May be <code>null</code> if the bed was already broken, or if the bed hasn't been placed yet
   * @see #destroyBedNaturally(BedDestructionInfo)
   */
  @Nullable
  default ArenaBedBreakEvent destroyBedNaturally(Team team, @Nullable Player player) {
    final BedDestructionInfo info = BedDestructionInfo.construct(team);

    info.setDestroyer(player);

    return destroyBedNaturally(info);
  }

  /**
   * Will destroy the bed as if a player actually broke it ingame.
   *
   * @param info An info object containing information about the bed destruction
   * @return The event containing the result. May be <code>null</code> if the bed was already broken, or if the bed hasn't been placed yet
   */
  @Nullable
  ArenaBedBreakEvent destroyBedNaturally(BedDestructionInfo info);

  /**
   * Returns the authors of the arena.
   *
   * @return The authors or creators of the arena
   */
  String[] getAuthors();

  /**
   * Returns all authors in a string split up by a comma.
   * <p>
   * 	E.g. it might return: "Notch, Marcel, Obama"<br>
   * 	Returns "nobody" in the language of the sender if there are none
   * </p>
   *
   * @param sender Returns in the language of this sender
   * @return The authors of the arena represented in a string
   */
  String getDisplayedAuthors(@Nullable CommandSender sender);

  /**
   * Returns all authors in a string split up by a comma.
   * <p>
   * 	E.g. it might return: "Notch, Marcel, Obama"<br>
   * 	Returns "nobody" in the configured default language if there are none
   * </p>
   *
   * @return The authors of the arena represented in a string
   */
  String getDisplayedAuthors();

  /**
   * Adds an author.
   *
   * @param author The author that shall be added
   */
  void addAuthor(String author);

  /**
   * Adds multiple authors.
   *
   * @param authors The authors that shall be added
   */
  void addAuthors(Collection<String> authors);

  /**
   * Removes an author.
   *
   * @param author The author that shall be removed
   * @return <code>false</code> if there's no author with that name
   */
  boolean removeAuthor(String author);

  /**
   * Removes all existing authors.
   *
   * @return The amount of authors that have been removed
   */
  int removeAllAuthors();

  /**
   * Calculates [teams amount] x [players per team] when using a normal arena.
   * <p>
   * 	It'll take the configured one when using a voting arena
   * </p>
   *
   * @return The maximum amount of players who can join at the same time
   */
  int getMaxPlayers();

  /**
   * Returns the minimum amount of players that are needed to start match.
   * <p>
   * 	While it's not logical to have less than two, it's still legal to have any number (even one that is a minus number).
   * </p>
   * @return The minimum amount of players needed to start a game
   */
  int getMinPlayers();

  /**
   * Set the minimum amount of players that are needed to start a match.
   * <p>
   * 	While it's not logical to have less than two, it's still legal to have any number (even one that is a minus number).
   * </p>
   *
   * @param minPlayers The new minimum amount of players needed to start a game
   */
  void setMinPlayers(int minPlayers);


  /**
   * Returns the amount of players that can be in a team.
   * <p>
   * 	When the arena is {@link RegenerationType#VOTING} then it'll return the max amount of players in the arena
   * </p>
   *
   * @return The configured amount of players that can be in a team
   */
  int getPlayersPerTeam();

  /**
   * Set the amount of players that can be in a team.
   * <p>
   * 	When the arena is {@link RegenerationType#VOTING}, then it'll set the max amount of players in the arena
   * </p>
   *
   * @param playersPerTeam The new configured amount of players that can be in a team
   */
  void setPlayersPerTeam(int playersPerTeam);

  /**
   * Returns the world in which the arena is located at.
   *
   * @return Might return null if the world doesn't exist
   */
  @Nullable World getGameWorld();

  /**
   * Returns the name of the world in which the game is located at.
   *
   * @return The configured world name in which the arena is located at
   */
  String getGameWorldName();

  /**
   * Set the name of the world in which the game is located at.
   * Use <code>null</code> to reset it back to default (to remove it).
   *
   * @param name The new game world. <code>null</code> to reset it back to default
   */
  void setGameWorldName(@Nullable String name);

  /**
   * An arena of the type {@link RegenerationType#REGION} has two corners.
   * This method returns the point with the lowest ({@link Math#min(int, int)} number.
   * <p>
   * It returns <code>null</code> if no points have been set or when the type is not {@link RegenerationType#REGION}.
   *
   * @return The lower corner of the cubic arena. Possibly <code>null</code>
   */
  @Nullable XYZ getMinRegionCorner();

  /**
   * An arena of the type {@link RegenerationType#REGION} has two corners.
   * This method returns the point with the greatest ({@link Math#max(int, int)} number.
   * <p>
   * It returns <code>null</code> if no points have been set or when the type is not {@link RegenerationType#REGION}.
   *
   * @return The greater corner of the cubic arena. Possibly <code>null</code>
   */
  @Nullable XYZ getMaxRegionCorner();

  /**
   * An arena of the type {@link RegenerationType#REGION} has two corners.
   * This method sets them.
   * <p>
   * <b>Important:</b><br>
   * This does <u>not</u> automatically save the blocks of the arena.
   * You likely want to run {@link #runRegenerationBlocksSavingProcess(Consumer)} afterwards.
   * <p>
   * You may not pass <code>null</code> as an argument.
   * Use {@link #unsetRegionCorners()} if you want to remove them.
   *
   * @param pos1 The points of the first corner
   * @param pos2 The points of the second corner
   */
  void setRegionCorners(XYZ pos1, XYZ pos2);

  /**
   * Removes any set corners in the arena.
   * <p>
   * Keep in mind that the arena gets unplayable by this.
   */
  void unsetRegionCorners();

  /**
   * Sets the world in which the game is located at.
   * <p>
   * <b>Important:</b><br>
   * This does <u>not</u> automatically save the blocks of the arena.
   * You likely want to run {@link #runRegenerationBlocksSavingProcess(Consumer)} afterwards.
   *
   * @param world The new world, possibly <code>null</code> to unset it
   */
  void setGameWorld(@Nullable World world);

  /**
   * The lobby location which can be set with /bw arena setlobby
   *
   * @return Might return null when no lobby has been set
   */
  @Nullable Location getLobbyLocation();

  /**
   * Returns a List of all players that are in a specific team.
   *
   * <p>
   *  You may pass <code>null</code> to look for players that haven't picked a team (works only during lobby).
   * </p>
   *
   * @param team The team in which the players are. <code>null</code> to find players who haven't picked a team
   * @return All players that are inside the given team
   */
  List<Player> getPlayersInTeam(@Nullable Team team);

  /**
   * Returns a List of all teams that still have a bed.
   *
   * <p>
   *   Note this will not return teams that have no players but are still alive because of solo reconnect.
   *   If you want to get teams that currently have players online, use {@link #getTeamsWithPlayers()}.
   * </p>
   *
   * @return All currently involved teams in the match
   */
  List<Team> getAliveTeams();

  /**
   * Returns a List of all teams that have at least one player.
   *
   * <p>
   *   Use <code>ignorePlayers</code> to ignore these players in the result.
   *   Meaning it'll return all teams minus <code>ignorePlayers</code> that have at least one player.
   *   Note this will not return teams that have no players but are still alive because of solo reconnect.
   *   If you want to get teams that currently have players online, use {@link #getTeamsWithPlayers(Player...)}.
   * </p>
   *
   * @param ignorePlayers Will ignore these teams in the result and act as if they weren't ingame
   * @return All currently involved teams in the match
   */
  List<Team> getAliveTeams(Player... ignorePlayers);

  /**
   * Returns a List of all teams that have at least one player.
   *
   * <p>
   *   Note this will not return teams that have at least one player, however it it possible teams are still alive because of solo reconnect.
   *   If you want to get all teams that still have a bed, use {@link #getAliveTeams()}.
   * </p>
   *
   * @return All currently involved teams in the match
   */
  List<Team> getTeamsWithPlayers();

  /**
   * Returns a List of all teams that have at least one player.
   *
   * <p>
   *   Use <code>ignorePlayers</code> to ignore these players in the result.
   *   Meaning it'll return all teams minus <code>ignorePlayers</code> that have at least one player.
   *   Note this will not return teams that have at least one player, however it it possible teams are still alive because of solo reconnect.
   *   If you want to get all teams that still have a bed, use {@link #getAliveTeams(Player...)}.
   * </p>
   *
   * @param ignorePlayers Will ignore these teams in the result and act as if they weren't ingame
   * @return All currently involved teams in the match
   */
  List<Team> getTeamsWithPlayers(Player... ignorePlayers);

  /**
   * Set the location for the lobby to which the players will be teleported at when they join the arena.
   *
   * @param location The new location where the lobby is located at
   */
  void setLobbyLocation(@Nullable Location location);

  /**
   * Returns whether a location for the lobby has been set.
   *
   * @return Whether a lobby location has been set yet
   */
  default boolean hasLobbyLocation() {
    return this.getLobbyLocation() != null;
  }

  /**
   * Gets the icon of the arena.
   *
   * @return The icon that's being used in GUIs
   */
  ItemStack getIcon();

  /**
   * Set the new icon for the arena.
   *
   * @param icon The new icon that'll be used in GUIs
   */
  void setIcon(ItemStack icon);

  /**
   * Gets the arena's {@link ArenaWeatherType}
   *
   * @return the arena's weather type
   */
  ArenaWeatherType getWeatherType();

  /**
   * Sets the arena's {@link ArenaWeatherType}
   *
   * @param weatherType the new arena weather type
   */
  void setWeatherType(ArenaWeatherType weatherType);

  /**
   * Gets the arena's {@link ArenaTimeType}
   *
   * @return the arena's time type
   */
  ArenaTimeType getTimeType();

  /**
   * Sets the arena's {@link ArenaTimeType}
   *
   * @param timeType the new arena time type
   */
  void setTimeType(ArenaTimeType timeType);

  /**
   * Returns a collection of all added spawners
   *
   * @return All added spawners
   */
  Collection<Spawner> getSpawners();

  /**
   * Adds a spawner to the arena
   *
   * @param location The location of the spawner
   * @param type Info about what it shall drop
   * @return The spawner instance with information about it
   */
  Spawner addSpawner(XYZ location, DropType type);

  /**
   * Returns all spawners that are located at that block/location
   *
   * @param location The location at which they are located at
   * @return All spawners at that location
   */
  Spawner[] getSpawnersAtLocation(IntXYZ location);

  /**
   * Returns all spawners that are located at that block/location
   *
   * @param location The location at which they are located at
   * @return All spawners at that location
   */
  Spawner[] getSpawnersAtLocation(XYZ location);

  /**
   * Returns all spawners that are located at that block/location
   *
   * @param location The location at which they are located at
   * @return All spawners at that location
   */
  Spawner[] getSpawnersAtLocation(Location location);

  /**
   * Same as {@link Arena#addPlayer(Player, Team, AddPlayerCause)}, but player won't join any team automatically
   * and passes {@link AddPlayerCause#PLUGIN} as the reason for the player joining the arena.
   *
   * @param player The player who shall enter the arena
   * @return Returns if it was successful, and if not what caused it
   */
  default @Nullable AddPlayerIssue addPlayer(Player player) {
    return addPlayer(player, null, AddPlayerCause.PLUGIN);
  }

  /**
   * Same as {@link Arena#addPlayer(Player, Team, AddPlayerCause)}, passes {@link AddPlayerCause#PLUGIN} as the reason for the player joining the arena.
   *
   * @param player The player who shall enter the arena
   * @param team The team it shall automatically enter. If null is given then he won't join any team
   * @return Returns if it was successful, and if not what caused it
   */
  default @Nullable AddPlayerIssue addPlayer(Player player, @Nullable Team team) {
    return addPlayer(player, team, AddPlayerCause.PLUGIN);
  }

  /**
   * Tries to add the player to the arena.
   * <p>
   *     Doesn't send a message in the chat and only works while {@link #getStatus()} returns {@link ArenaStatus#LOBBY},
   * </p>
   *
   * @param player The player who shall enter the arena
   * @param team The team it shall automatically enter. If null is given then he won't join any team
   * @param cause What has made the player to join the arena. Preferably you want to use {@link AddPlayerCause#PLUGIN}
   * @return Returns if it was successful, and if not what caused it
   * @throws IllegalStateException When the state of the arena isn't {@link ArenaStatus#LOBBY} or when the player isn't online
   */
  @Nullable AddPlayerIssue addPlayer(Player player, @Nullable Team team, AddPlayerCause cause);

  /**
   * Tries to make the player rejoin an already active game.
   * <p>
   *     Keep in mind that the player has to be an active player on the match before and that the state is {@link ArenaStatus#RUNNING}.
   * </p>
   *
   * @param player The player who shall be readded to the game
   * @return The issue that prevents him from rejoining. <code>null</code> when there wasn't any
   */
  @Nullable RejoinPlayerIssue rejoinPlayer(Player player);

  /**
   * Tries to make the player rejoin an already active game using some rejoin info contained in the memory.
   * <p>
   * 	 *     Keep in mind that the player has to be an active player on the match before (doesn't have to be when <code>memory</code> is not null) and that the state is {@link ArenaStatus#RUNNING}.
   * 	 * </p>
   *
   * @param player The player who shall be readded to the game
   * @param memory The info stored before he left the match. May be a custom instance. When <code>null</code>, then it tries to obtain its internal stored info
   * @return The issue that prevents him from rejoining. <code>null</code> when there wasn't any
   */
  @Nullable RejoinPlayerIssue rejoinPlayer(Player player, @Nullable QuitPlayerMemory memory);


  /**
   * Does the same as {@link Arena#kickPlayer(Player, KickReason)},
   * but passes {@link KickReason#PLUGIN} as the reason.
   *
   * @param player The player who shall get kicked
   * @return Returns <code>false</code> if the player isn't playing inside the arena, otherwise <code>true</code>
   */
  default boolean kickPlayer(Player player) {
    return kickPlayer(player, KickReason.PLUGIN);
  }

  /**
   * Kicks the player from the arena. Won't kick spectators.
   *
   * @param player The player who shall get kicked
   * @param reason Reason why he got kicked
   * @return Returns <code>false</code> if the player isn't playing inside the arena, otherwise <code>true</code>
   */
  boolean kickPlayer(Player player, KickReason reason);

  /**
   * Does the same as {@link Arena#kickAllPlayers(KickReason)},
   * but uses {@link KickReason#PLUGIN} as the reason.
   *
   * @return The amount of players that have been kicked
   */
  default int kickAllPlayers() {
    return kickAllPlayers(KickReason.PLUGIN);
  }

  /**
   * Kicks every player who's currently inside the arena.
   * <p>
   * Spectators won't get kicked by this method.
   *
   * @param reason Reason why they got kicked
   * @return The amount of players that have been kicked
   */
  default int kickAllPlayers(KickReason reason) {
    int amount = 0;

    for (Player player : getPlayers()) {
      if (kickPlayer(player, reason))
        amount++;
    }

    return amount;
  }

  /**
   * Returns the team of a player.
   * Can return <code>null</code> when the player isn't actually inside the arena or
   * when he hasn't been added to a team (only possible during lobby phase).
   *
   * @param player The player we want to check
   * @return The team of the player, might be <code>null</code>
   */
  @Nullable Team getPlayerTeam(Player player);

  /**
   * Change the team of a player.
   * <p>
   *     It's okay to pass <code>null</code> as the team if you want to remove him from any,
   *     but this only works during the lobby phase.
   * </p>
   * <p>
   *     Note that nothing happen if the team reached the {@link #getPlayersPerTeam()} amount.
   * </p>
   *
   * @param player The player whose team we want to change
   * @param team His new team
   * @throws IllegalStateException When passing <code>null</code> as the team outside the lobby phase
   */
  void setPlayerTeam(Player player, @Nullable Team team);

  /**
   * Simulates as if the player would manually want to change the team.
   * <p>
   *  Sends messages etc. if it was successful or something went wrong.
   * </p>
   *
   * @param player The player whose team we want to change
   * @param team His new team
   * @return <code>true</code> if his team changed, <code>false</code> if not
   */
  boolean moveToTeamDuringLobby(Player player, Team team);

  /**
   * Simulates as if the player would manually leave his current team.
   * <p>
   *   Sends messages etc. if it was successful or something went wrong.
   * </p>
   *
   * @param player The player whose team we want to remove
   * @return <code>true</code> if his team has been removed, <code>false</code> if not
   */
  boolean leaveTeamDuringLobby(Player player);

  /**
   * Returns all players (except spectators) who are inside the lobby/match playing.
   *
   * @return All players who are currently inside the arena. Spectators are not included
   */
  Collection<Player> getPlayers();

  /**
   * Get whether the player is currently playing in this arena.
   * <p>
   *   This does not include spectators. This is just a more efficient
   *   check of {@link #getPlayers()}.
   * </p>
   *
   * @param player The player we want to check
   * @return <code>true</code> if the player is currently playing in the arena
   */
  boolean isPlaying(Player player);

  /**
   * Checks whether the player is currently spectating this arena.
   * <p>
   *   This does not include players who are currently playing in the arena.
   *   There may be exceptions, where a player might be playing and spectating at the same time,
   *   such as when he is death spectating (waiting to respawn).
   * </p>
   *
   * @param player The player we want to check
   * @return <code>true</code> if the player is currently spectating the arena
   */
  boolean isSpectating(Player player);

  /**
   * Returns all memories/infos of all players who were playing in the game but left.
   * <p>
   *     Is empty when the game is not running anymore and doesn't contain instances of players who rejoined.
   * </p>
   *
   * @return All memories of players who left a running match
   */
  Collection<QuitPlayerMemory> getQuitPlayerMemories();

  /**
   * Returns all memory/infos of a player who has left a running match.
   *
   * @param playerUUID The id of the player
   * @return The memory of the player. <code>null</code> when there isn't any
   */
  @Nullable QuitPlayerMemory getQuitPlayerMemory(UUID playerUUID);

  /**
   * Same as {@link Arena#addSpectator(Player, SpectateReason)}.
   * <p>
   * Uses {@link SpectateReason#PLUGIN} as the reason
   *
   * @param player The player who shall spectate the arena
   * @return An instance that contains spectating informations about the player. Null if failed because he's already spectating or something
   */
  default @Nullable Spectator addSpectator(Player player) {
    return addSpectator(player, SpectateReason.PLUGIN);
  }

  /**
   * Adds a player as a spectator.
   * <p>
   * Should only be done when the arena is running, otherwise
   * problems might occur.
   *
   * @param player The player who shall spectate the arena
   * @param reason What caused the player to be a spectator
   * @return An instance that contains spectating informations about the player. Null if failed because he's already spectating or something
   */
  @Nullable Spectator addSpectator(Player player, SpectateReason reason);

  /**
   * Looks and returns the spectator data of the player.
   * <p>
   * Also checks if the arena is equivalent to this one and returns null if it's not.
   *
   * @param player The player it should look up from
   * @return The spectate data from the player. Null if the player isn't spectating
   */
  @Nullable Spectator getSpectateData(Player player);

  /**
   * Returns every player who's currently spectating this arena.
   *
   * @return All players who are currently spectating the arena
   */
  Collection<Player> getSpectators();

  /**
   * Returns the spectator data of every player who's currently spectating this arena
   *
   * @return The spectate data of all players who are spectating
   */
  Collection<Spectator> getEverySpectatorData();

  /**
   * Does the same as {@link Arena#kickAllSpectators(KickSpectatorReason)},
   * but uses {@link KickSpectatorReason#PLUGIN} as the reason.
   *
   * @return The amount of players that have been kicked
   */
  default int kickAllSpectators() {
    return kickAllSpectators(KickSpectatorReason.PLUGIN);
  }

  /**
   * Kicks every spectator of this arena
   *
   * @param reason Reason why they got kicked
   * @return The amount of players that have been kicked
   */
  default int kickAllSpectators(KickSpectatorReason reason) {
    int amount = 0;

    for (Spectator spectator : getEverySpectatorData()) {
      if (spectator.kick(reason))
        amount++;
    }

    return amount;
  }

  /**
   * Sends this message to every player and spectator
   *
   * @param message The message that shall get sent
   */
  void broadcast(String message);

  /**
   * Sends this message to every player and spectator
   *
   * @param message The message that shall get sent
   */
  void broadcast(Message message);

  /**
   * Plays the sound to every player and spectator
   *
   * @param sound The sound that shall get played
   */
  void broadcast(VarSound sound);

  /**
   * Checks whether the given location is inside the arena.
   * <p>
   *     Always returns false for non-normal arenas ({@link RegenerationType#isNormal()})
   * </p>
   *
   * @param location The location we want to check
   * @return If the location is inside the arena
   */
  boolean isInside(Location location);

  /**
   * Checks whether the given location is inside the arena.
   * <p>
   *     Always returns false for non-normal arenas ({@link RegenerationType#isNormal()}).
   *     Keep in mind that it'll always return true for {@link RegenerationType#WORLD}.
   *     You might want to use {@link #isInside(Location)} for a more precise response.
   * </p>
   *
   * @param location The location we want to check
   * @return If the location is inside the arena
   */
  boolean isInside(XYZ location);

  /**
   * Checks whether the given location is inside the arena.
   * <p>
   *     Always returns false for non-normal arenas ({@link RegenerationType#isNormal()}).
   *     Keep in mind that it'll always return true for {@link RegenerationType#WORLD}.
   *     You might want to use {@link #isInside(Location)} for a more precise response.
   * </p>
   *
   * @param location The location we want to check
   * @return If the location is inside the arena
   */
  boolean isInside(IntXYZ location);

  /**
   * Returns the time when the arena changed the last time its state to {@link ArenaStatus#RUNNING}.
   * <p>
   *  May be <code>null</code> if no match has been played on this arena since server start.
   * </p>
   *
   * @return The time when the last match has started. May be <code>null</code>
   */
  @Nullable
  Instant getRoundStartTime();

  /**
   * Returns the time when the arena has ended (forceful state changes don't count).
   * <p>
   *  May be <code>null</code> if the arena hasn't ended yet or it got into the {@link ArenaStatus#LOBBY} state.
   * </p>
   *
   * @return The time when the last match has ended. May be <code>null</code>
   */
  @Nullable
  Instant getRoundStopTime();

  /**
   * Get the time since the current match is running or how long the last has match.
   * <p>
   *   May be <code>null</code> if no match has started on this arena since server start.
   * </p>
   *
   * @return The time the arena is already running. May be <code>null</code>
   */
  @Nullable
  Duration getRunningTime();

  /**
   * Forcefully update the scoreboard for any player and spectator
   */
  void updateScoreboard();

  /**
   * Removes the arena and kicks all playing players + spectators.
   * <p>
   * Might fail when the arena has already been removed or a plugin cancells the operation via the {@link ArenaDeleteEvent}.
   *
   * @return <code>true</code> when the operation was successful
   */
  boolean remove();

  /**
   * Returns how much time is left until the game starts ({@link ArenaStatus#LOBBY}) or until all players get kicked ({@link ArenaStatus#END_LOBBY}).
   * Might return -1 when the status isn't {@link ArenaStatus#isLobby()} or when the timer isn't running (e.g. not enough players in the lobby).
   *
   * @return The time remaining for the lobby showdown. <code>-1</code> if the requirements aren't met.
   */
  double getLobbyTimeRemaining();

  /**
   * Set the new time until the lobby showdown is being executed.
   * <p>
   * Might fail when the state isn't {@link ArenaStatus#LOBBY}.
   *
   * @param value The new time until the lobby showdown is being executed
   * @param instant For the expbar animation. If <code>false</code> it'll "slide" from the current value to the new value
   * @return If the change has been applied
   */
  boolean setLobbyTimeRemaining(double value, boolean instant);

  /**
   * Returns how much time is left (in seconds) until an active match is ending.
   * <p>
   *     The state has to be {@link ArenaStatus#RUNNING} and the timer has to actually be active ({@link #isIngameTimerTicking()}), otherwise this method returns -1.
   * </p>
   *
   * @return The time in ticks until a match is ending. -1 when the timer isn't running
   */
  int getIngameTimeRemaining();

  /**
   * Set the time (in seconds) until an active match is ending.
   *
   * @param timeInSeconds The new time in seconds
   */
  void setIngameTimeRemaining(int timeInSeconds);

  /**
   * Returns whether or not currently the ingame timer is countding down
   *
   * @return <code>true</code> when the ingame timer is currently active
   */
  boolean isIngameTimerTicking();

  /**
   * Set the new time until the lobby showdown is being executed.
   * <p>
   * Might fail when the state isn't {@link ArenaStatus#LOBBY}.
   * <p>
   * This method is similar to {@link #setLobbyTimeRemaining(double, boolean)}, but passes true to the instant parameter.
   *
   * @param value The new time until the lobby showdown is being executed
   * @return If the change has been applied
   */
  default boolean setLobbyTimeRemaining(double value) {
    return setLobbyTimeRemaining(value, true);
  }

  /**
   * Teleports the player inside this arena.
   * It has the same effect as if you'd write /bw arena tp.
   *
   * @param player The player who shall get teleported
   * @param sendMessage If it shall send a success/error message to the player error not.
   * @see #teleportHere(Player)
   */
  void teleportHere(Player player, boolean sendMessage);

  /**
   * Teleports the player inside this arena.
   * It has the same effect as if you'd write /bw arena tp.
   * <p>
   * This method is similar to {@link #teleportHere(Player, boolean)}, but won't display any success or error message to the player.
   *
   * @param player The player who shall get teleported
   * @see #teleportHere(Player, boolean)
   */
  default void teleportHere(Player player) {
    teleportHere(player, false);
  }

  /**
   * Usually the plugin prevents players from getting teleported and automatically kicks them from the game if they try to.
   * If that's the case depends on the configuration of the player (more exact on the kick-outofarena config).
   *
   * @param player The player who shall get teleported
   * @param target The location to which he shall get teleported to
   */
  void teleport(Player player, Location target);

  /**
   * There are configurations which limit the block breaking to blocks that have been placed by players.
   * Using this method you're able to mark blocks as player placed.
   * Use {@link GameAPI#isPlayerBlockMarkingSupported()} to check if it's enabled.
   *
   * @param block The block that shall be marked/unmarked as player-placed
   * @param newState <code>true</code> if it has been placed by a player
   * @see #isBlockPlayerPlaced(Block)
   */
  void setBlockPlayerPlaced(Block block, boolean newState);

  /**
   * There are configurations which limit the block breaking to blocks that have been placed by players.
   * Using this method you'll be able to check if a block has been placed by a player.
   * Use {@link GameAPI#isPlayerBlockMarkingSupported()} to check if has been enabled by the user, otherwise this method always returns false.
   *
   * @param block The block that shall be checked
   * @return <code>true</code> if it has been placed by a player
   * @see #setBlockPlayerPlaced(Block, boolean)
   */
  boolean isBlockPlayerPlaced(Block block);

  /**
   * Returns whether a player would be able to place a block at a specific location.
   * Generally the plugin limits the places where it's possible to place a block, you may use this method to identify where it is or not.
   * <p>
   * You may pass the material of the block that'd theoretically be placed at the given location.
   * It's not possible to place some certain types of materials, passing a non-null value will make the check more strict.
   * Make sure it's actually a Material for a block, and not one for an item as it'll be more likely that the check will fail in that case.
   * <p>
   * Keep in mind that it won't actually place the block. You may use the following example for simulating a block that's being placed by a player:
   * <pre>{@code
   * Arena arena = ...;
   * Block target = ...;
   * Material newMaterial = Material.STONE;
   *
   * if (arena.canPlaceBlockAt(target, newMaterial)) {
   *     target.setType(newMaterial);
   *     arena.setBlockPlayerPlaced(target, true);
   * }
   * }</pre>
   *
   * @param loc The location of the theoretical block
   * @param blockMaterial (Optional) the material of the block that'd theoretically be placed. Use <code>null</code> to not check that as well
   * @return <code>true</code> when a player could place a block at the given location
   * @deprecated Might break in the future when Spigot releases their BlockType API
   */
  @Deprecated
  boolean canPlaceBlockAt(Location loc, @Nullable Material blockMaterial);

  /**
   * Returns whether a player would be able to place a block at a specific location.
   * Generally the plugin limits the places where it's possible to place a block, you may use this method to identify where it is or not.
   * <p>
   * You may pass the material of the block that'd theoretically be placed at the given location.
   * It's not possible to place some certain types of materials, passing a non-null value will make the check more strict.
   * Make sure it's actually a Material for a block, and not one for an item as it'll be more likely that the check will fail in that case.
   * <p>
   * Keep in mind that it won't actually place the block. You may use the following example for simulating a block that's being placed by a player:
   * <pre>{@code
   * Arena arena = ...;
   * Block target = ...;
   * Material newMaterial = Material.STONE;
   *
   * if (arena.canPlaceBlockAt(target, newMaterial)) {
   *     target.setType(newMaterial);
   *     arena.setBlockPlayerPlaced(target, true);
   * }
   * }</pre>
   *
   * @param block The theoretical block
   * @param blockMaterial (Optional) the material of the block that'd theoretically be placed. Use <code>null</code> to not check that as well
   * @return <code>true</code> when a player could place a block at the given location
   * @deprecated Might break in the future when Spigot releases their BlockType API
   */
  @Deprecated
  default boolean canPlaceBlockAt(Block block, @Nullable Material blockMaterial) {
    return canPlaceBlockAt(block.getLocation(), blockMaterial);
  }

  /**
   * Returns whether a player would be able to place a block at a specific location.
   * Generally the plugin limits the places where it's possible to place a block, you may use this method to identify where it is or not.
   * <p>
   * You might want to use {@link #canPlaceBlockAt(Location, Material)} to additionally check whether it's possible to place a certain material of a block.
   *
   * @param loc The location of the theoretical block
   * @return <code>true</code> when a player could place a block at the given location
   */
  default boolean canPlaceBlockAt(Location loc) {
    return canPlaceBlockAt(loc, null);
  }

  /**
   * Returns whether a player would be able to place a block at a specific location.
   * Generally the plugin limits the places where it's possible to place a block, you may use this method to identify where it is or not.
   * <p>
   * You might want to use {@link #canPlaceBlockAt(Block, Material)} to additionally check whether it's possible to place a certain material of a block.
   *
   * @param block The theoretical block
   * @return <code>true</code> when a player could place a block at the given location
   */
  default boolean canPlaceBlockAt(Block block) {
    return canPlaceBlockAt(block.getLocation(), null);
  }

  /**
   * Returns all the ItemStacks that will be given to a player when they spawn with
   * all dyes already applied, and enchantments added.
   *
   * @param player the player that the list of items should be generated for
   * @param team the team the items shall be generated for (used for dyeing, and getting team upgrade enchants)
   * @param firstSpawn if we are assuming this is a first spawn or a respawn
   * @param includeKeepOnDeath if the list should include keep-on-death items
   * @return all the ItemStacks that would be given to the player when they spawn
   */
  Collection<ItemStack> getItemsGivenOnSpawn(Player player, Team team, boolean firstSpawn, boolean includeKeepOnDeath);

  /**
   * Returns the same ItemStack but possibly dyed or enchanted depending on team upgrades,
   * and previous player purchases.
   *
   * @param itemStack the ItemStack we want team characteristics to be applied to
   * @param player the player that the item should be formatted for (used to gather correct enchants)
   * @param team the team the item should be formatted for (used to apply dyes, and gather team upgrade enchants)
   * @return the formatted version of the same ItemStack
   */
  ItemStack formatItemStack(ItemStack itemStack, @Nullable Player player, Team team);

  /**
   * Whether or not a player can bypass team traps
   *
   * @param player the player we are checking
   * @return whether or not they have the ability to bypass traps
   */
  boolean canPlayerBypassTrap(Player player);

  /**
   * Whether or not the players ability to bypass teams traps will remain if
   * they die. Will also return false if the player does not currently have the ability
   *
   * @param player the player we are checking
   * @return if the ability to bypass traps is kept on death
   */
  boolean isPlayerTrapBypassKeepOnDeath(Player player);

  /**
   * Removes a player's ability to bypass team traps
   *
   * @param player the player who is losing the ability to bypass traps
   */
  void removePlayerTrapBypass(Player player);

  /**
   * Gives a player the ability to bypass team traps
   *
   * @param player the player gaining the ability to bypass team traps
   * @param keepOnDeath whether or not this ability should be persistent on death
   */
  void addPlayerTrapBypass(Player player, boolean keepOnDeath);

  /**
   * The saving process of the blocks inside the arenas (aka regeneration blocks) is being executed async.
   * This method returns if it's currently running or not.
   *
   * @return Whether the arenas inside the block are currently being fetched and saved.
   * @see #runRegenerationBlocksSavingProcess()
   * @see #runRegenerationBlocksSavingProcess(Consumer)
   */
  boolean isRegenerationBlocksSavingProcessRunning();

  /**
   * The saving process of the blocks inside the arenas (aka regeneration blocks) is being executed async.
   * This method runs the process.
   * <p>
   * 	Keep in mind that the arena has to be stopped ({@link ArenaStatus#STOPPED} and the process may not be running already {@link #isRegenerationBlocksSavingProcessRunning()}.
   * 	The optional callback will immediatly return <code>false</code> in those cases.
   * </p>
   *
   * @param finishCallback Gets called (possibly in a second thread) when the process is done. The parameter inside the callback tells you if the process was sucessfull or not.
   * @see #runRegenerationBlocksSavingProcess()
   * @see #isRegenerationBlocksSavingProcessRunning()
   */
  void runRegenerationBlocksSavingProcess(@Nullable Consumer<Boolean> finishCallback);

  /**
   * The saving process of the blocks inside the arenas (aka regeneration blocks) is being executed async.
   * This method runs the process.
   * <p>
   * 	Keep in mind that the arena has to be stopped ({@link ArenaStatus#STOPPED} and the process may not be running already {@link #isRegenerationBlocksSavingProcessRunning()}.
   * 	You might want to use {@link #runRegenerationBlocksSavingProcess(Consumer)} if you want to know when it is done.
   * </p>
   *
   * @see #runRegenerationBlocksSavingProcess(Consumer)
   * @see #isRegenerationBlocksSavingProcessRunning()
   */
  default void runRegenerationBlocksSavingProcess() {
    this.runRegenerationBlocksSavingProcess(null);
  }

  /**
   * Returns all the issues that were detected by this plugin (and not manually added by others) that prevent the arena from running.
   * <p>
   * You most likely want to use {@link #getIssues(CommandSender)} instead as other plugins are able to add their own issues as well using the {@link ArenaIssuesCheckEvent}.
   * This method does not count those in.
   *
   * @param sender Will translate the message into the language of the sender. Passing <code>null</code> causes it to be in the default configured language
   * @return All the issues that prevent it from running. If there are none then there's nothing preventing it.
   * @see #getDefaultIssues()
   * @see #getIssues(CommandSender)
   * @see #getIssues()
   */
  Set<ArenaIssuesCheckEvent.Issue> getDefaultIssues(@Nullable CommandSender sender);

  /**
   * Returns all the issues that were detected by this plugin (and not manually added by others) that prevent the arena from running.
   * <p>
   * You most likely want to use {@link #getIssues()} instead as other plugins are able to add their own issues as well using the {@link ArenaIssuesCheckEvent}.
   * This method does not count those in.
   *
   * @return All the issues that prevent it from running. If there are none then there's nothing preventing it.
   * @see #getDefaultIssues(CommandSender)
   * @see #getIssues(CommandSender)
   * @see #getIssues()
   */
  default Set<ArenaIssuesCheckEvent.Issue> getDefaultIssues() {
    return getDefaultIssues(null);
  }

  /**
   * Returns all the issues that prevent the arena from running.
   * <p>
   * 	It'll use {@link ArenaIssuesCheckEvent} as well to check wether other plugins added additional issues.
   * </p>
   * The plugin uses the same method when it loads all the arenas during start up or when manually trying to start an arena.
   *
   * @param sender Will translate the message into the language of the sender. Passing <code>null</code> causes it to be in the default configured language
   * @return All the issues that prevent it from running. If there are none then there's nothing preventing it.
   * @see #getIssues()
   * @see #getDefaultIssues()
   * @see #getDefaultIssues(CommandSender)
   */
  Set<ArenaIssuesCheckEvent.Issue> getIssues(@Nullable CommandSender sender);

  /**
   * Returns all the issues that prevent the arena from running.
   * <p>
   * It'll use {@link ArenaIssuesCheckEvent} as well to check wether other plugins added additional issues.
   * <p>
   * The plugin uses the same method when it loads all the arenas during start up or when manually trying to start an arena.
   *
   * @return All the issues that prevent it from running. If there are none then there's nothing preventing it.
   * @see #getIssues(CommandSender)
   * @see #getDefaultIssues()
   * @see #getDefaultIssues(CommandSender)
   */
  Set<ArenaIssuesCheckEvent.Issue> getIssues();

  /**
   * Returns the current buy-group level of the player.
   * <p>
   * Returns {@link BuyGroup#getLowestLevel()} as the initial value for players, even in cases when the given player is not playing in the arena.
   *
   * @param player The player whose level in the buy-group we want to find
   * @param group The corresponding buy-group
   * @return The players current level in the buy-group
   * @see #setBuyGroupLevel(Player, BuyGroup, int)
   */
  int getBuyGroupLevel(Player player, BuyGroup group);

  /**
   * Modify the buy-group level of the player.
   * <p>
   * It doesn't have any effect when the given player is not playing inside the arena and the arena currently isn't running {@link ArenaStatus#RUNNING}.
   *
   * @param player The player whose level we want to change
   * @param group The corresponding buy-group
   * @param level The new level
   * @see #getBuyGroupLevel(Player, BuyGroup)
   */
  void setBuyGroupLevel(Player player, BuyGroup group, int level);

  /**
   * Returns the spawnpoint of a spectator.
   * <p>
   *     It's possible that the method returns <code>null</code> as it's not required to have it set for a running arena.
   *     The plugin decides for a random points inside the arena if none is set.
   * </p>
   *
   * @return The spawnpoint for spectators, possibly <code>null</code>
   */
  @Nullable XYZYP getSpectatorSpawn();

  /**
   * Set the spawnpoint for spectators.
   * <p>
   *     It's okay for the value to be <code>null</code> as it's not required to have it set for a running arena.
   *     The plugin decides for a random points inside the arena if none is set.
   * </p>
   *
   * @param location The new spawnpoint for spectators, possibly <code>null</code>
   */
  void setSpectatorSpawn(@Nullable XYZYP location);

  /**
   * Obtain a teams private inventory using this method.
   * <p>
   *     Each team has a private inventory during a match that can be accessed for instance by an ender chest (depending on the configuration).
   *     This method returns the exact inventory instance that will be opened when a player would interact with it.
   * </p>
   *
   * @param team The team that owns the inventory
   * @return The private inventory of the team. <code>null</code> when the match is not running or when the team is not enabled
   */
  @Nullable Inventory getTeamPrivateInventory(Team team);

  /**
   * Obtain a players private chest inventory using this method.
   * <p>
   *     Each player has an a private inventory that they can access during a match using an Ender Chest.
   *     Note that that users can set Team Chests to be the Ender Chest block. In this case, this private chest inventory not be accessible to the player though an Ender Chest.
   *     See {@link #getTeamPrivateInventory(Team)} for team chest inventories.
   * </p>
   *
   * @param player the Player whose ender chest inventory we want to get
   * @return The private inventory of the player. <code>null</code> when the match is not running or when the player is not in a running arena
   */
  @Nullable Inventory getPlayerPrivateInventory(Player player);

  /**
   * Forcefully stop the match right now.
   * <p>
   *     This only works when the current state is {@link ArenaStatus#RUNNING}.
   *     The method will return <code>false</code> if it failed to end it.
   * </p>
   *
   * @param winningTeam The team that has won the match. Use <code>null</code> to mark it as a draw
   * @return If it was successful or not
   */
  boolean endMatch(@Nullable Team winningTeam);

  /**
   * Returns whether this arena is still existing.
   *
   * @return <code>true</code> if it still exists
   */
  boolean exists();

  /**
   * Applies the configured weather and time properties to the player.
   * <p>
   *     Uses {@link Player#setPlayerTime(long, boolean)} and {@link Player#setPlayerWeather(WeatherType)}.
   *     What exactly will happen greatly varies on the configurations on the user (e.g. whether "no-rain" is enabled).
   *     This method is useful when you've been modifying the players climate manually and would like to change it back to the normal state.
   * </p>
   * <p>
   *     This method doesn't care whether the player is currently inside the arena or not.
   * </p>
   *
   * @param player The target player
   */
  void applyPlayerClimate(Player player);

  /**
   * Returns whether this arena has been cloned from another arena.
   * <p>
   *     Cloned arenas aren't getting saved.<br>
   *     In case this method returns <code>true</code>, you may also expect a non-null value from {@link #getCloneParent()}.
   * </p>
   *
   * @return <code>true</code> in case this arena has been cloned from an other arena
   * @see #getCloneParent()
   * @see #getClones()
   * @see GameAPI#getCloningManager()
   */
  boolean isCloned();

  /**
   * Gets the arena from which this arena has been cloned from.
   * <p>
   *     Returns <code>null</code> when this arena hasn't been cloned.
   * </p>
   *
   * @return The arena from which this arena has been cloned from. May be <code>null</code>
   * @see #isCloned()
   * @see #getClones()
   * @see GameAPI#getCloningManager()
   */
  @Nullable
  Arena getCloneParent();

  /**
   * Gets all arenas that were cloned using this arena (basically the child arenas).
   *
   * @return All arenas that have been cloned from this arena
   * @see #isCloned()
   * @see #getCloneParent()
   * @see GameAPI#getCloningManager()
   */
  Arena[] getClones();

  /**
   * Save the arenas current state. Does not include the blocks file.
   * <p>
   *     The process is being executed on a separate thread.
   * </p>
   * <p>
   *     MBedwars is also handling file lockages and queues properly.
   *     This means that the saving process may not be executed immediately if there are prior tasks.
   * </p>
   */
  void saveAsync();

  /**
   * Save the arenas current state. Does not include the blocks file.
   * <p>
   *     Note that the saving process is being executed on the same thread as yours.
   * </p>
   * <p>
   *     MBedwars is also handling file lockages and queues properly.
   *     This means that the saving process may not be executed immediately if there are prior tasks.
   * </p>
   */
  void saveNow();

  /**
   * Define whether the player's time should be reset to default after one leaves.
   * <p>
   *     It is being reset back to default after a new match is being started.<br>
   *     Whether it is true or default by default depends on whether always-day is enabled.
   * </p>
   * <p>
   *     {@link Player#resetPlayerTime()} is being used.
   * </p>
   *
   * @param newValue The new value
   */
  void setResetPlayerTimeOnQuit(boolean newValue);

  /**
   * Define whether the player's weather should be reset to default after one leaves.
   * <p>
   *     It is being reset back to default after a new match is being started.<br>
   *     Whether it is true or default by default depends on whether no-rain is enabled.
   * </p>
   * <p>
   *     {@link Player#resetPlayerWeather()} is being used.
   * </p>
   *
   * @param newValue The new value
   */
  void setResetPlayerWeatherOnQuit(boolean newValue);

  /**
   * Gets whether the player's time shall be reset to default after one leaves.
   * <p>
   *     It is being reset back to default after a new match is being started.<br>
   *     Whether it is true or default by default depends on whether always-day is enabled.
   * </p>
   * <p>
   *     {@link Player#resetPlayerTime()} is being used.
   * </p>
   *
   * @return The current value
   */
  boolean isResetPlayerTimeOnQuit();

  /**
   * Gets whether the player's weather shall be reset to default after one leaves.
   * <p>
   *     It is being reset back to default after a new match is being started.<br>
   *     Whether it is true or default by default depends on whether no-rain is enabled.
   * </p>
   * <p>
   *     {@link Player#resetPlayerWeather()} is being used.
   * </p>
   *
   * @return The current value
   */
  boolean isResetPlayerWeatherOnQuit();

  /**
   * Tries to fetch the team of a location that's inside a base.
   * <p>
   *     This method will return <code>null</code> if there's no team at the given position (if it's outside of all bases)
   * </p>
   *
   * @param loc The location with which we shall check
   * @return The team's base that's at the given location. <code>null</code> if there's none
   * @see #getTeamByBaseLocation(XYZ)
   */
  @Nullable
  Team getTeamByBaseLocation(Location loc);

  /**
   * Tries to fetch the team of a location that's inside a base.
   * <p>
   *     This method will return <code>null</code> if there's no team at the given position (if it's outside of all bases)
   * </p>
   *
   * @param xyz The location with which we shall check
   * @return The team's base that's at the given location. <code>null</code> if there's none
   * @see #getTeamByBaseLocation(Location)
   */
  @Nullable
  Team getTeamByBaseLocation(XYZ xyz);

  /**
   * Gets a helper class for storing persistent information for exactly this arena instance.
   * <p>
   *   It may also be used to synchronize between servers.
   * </p>
   *
   * @return The persistant storage of this arena
   */
  ArenaPersistentStorage getPersistentStorage();

  /**
   * Returns the Arena instance that's being used internally.
   * <p>
   * 	There's no real use for third-party developers.
   * </p>
   *
   * @return The native arena instance
   * @param <T> Automatically casts the instance to the given generic
   */
  <T> T getNative();

  /**
   * Broadcast that a custom property has been changed (e.g. the value of an arena picker).
   * <p>
   *   This may be useful if you e.g. want to forcefully redraw all Arena GUIs (across all servers),
   *   as a custom arena picker variable has changed, whereby the arena isn't supposed to be visible anymore.
   * </p>
   * <p>
   *   This method also calls the {@link de.marcely.bedwars.api.event.arena.ArenaPropertyChangeEvent} and
   *   the {@link de.marcely.bedwars.api.event.remote.RemoteArenaPropertiesChangeEvent}.
   * </p>
   */
  void broadcastCustomPropertyChange();

  /**
   * Returns the voting handler of this arena.
   * <p>
   *   Check whether it's a voting arena using {@link #getRegenerationType()}.
   * </p>
   *
   * @return The voting handler of this arena. <code>null</code> if it's not a voting arena
   */
  @Nullable ArenaVotingHandler getVoting();

  /**
   * Returns the voting arena in which this arena is in the pool of.
   *
   * @return The voting arena in which this arena is in the pool of. <code>null</code> if it's not in any
   */
  @Nullable Arena getParticipatingVotingPool();

  /**
   * Get whether prizes (rewards in form of e.g. coins) will be given to players during this match.
   * <p>
   *   This method resets itself back to <code>true</code> after the match has ended.
   * </p>
   *
   * @return Whether prizes will be given to players during this match
   * @see #addPrizeForMatchDisabledTicket(String)
   * @see #removePrizeForMatchDisabledTicket(String)
   * @see #getPrizeForMatchDisabledTickets()
   */
  boolean isPrizeForMatchEnabled();

  /**
   * Set whether prizes (rewards in form of e.g. coins, achievements, stats ...) will be given to players during this match.
   * <p>
   *   This method resets itself back to <code>true</code> after the match has ended.
   * </p>
   * <p>
   *   The ticket id may be whatever you choose. It only has to be unique to keep the support for other plugins.
   * </p>
   *
   * @param ticketId The ticket id of whatever reason the prize for match has been disabled
   * @see #isPrizeForMatchEnabled()
   * @see #removePrizeForMatchDisabledTicket(String)
   */
  void addPrizeForMatchDisabledTicket(String ticketId);

  /**
   * Remove a ticket id from the list of reasons to disable prizes (rewards in form of e.g. coins, achievements, stats ...).
   *
   * @param ticketId The ticket id of whatever reason the prize for match has been disabled
   * @return <code>true</code> if the ticket id has been removed
   * @see #isPrizeForMatchEnabled()
   * @see #addPrizeForMatchDisabledTicket(String)
   */
  boolean removePrizeForMatchDisabledTicket(String ticketId);

  /**
   * Get a list of ticket ids of whatever reason the prize for match has been disabled.
   *
   * @return A list of ticket ids of whatever reason the prize for match has been disabled
   * @see #addPrizeForMatchDisabledTicket(String)
   * @see #removePrizeForMatchDisabledTicket(String)
   * @see #isPrizeForMatchEnabled()
   */
  Set<String> getPrizeForMatchDisabledTickets();

  /**
   * Get the type of a special chest existing within the arena.
   *
   * @param block The block we want to check
   * @return The type of the chest. <code>null</code> if it's not a special chest
   * @see #getChestInventory(Block, Player)
   */
  @Nullable
  PlayerOpenArenaChestEvent.ChestType getChestType(Block block);

  /**
   * Get the inventory the player would see with the given special chest.
   * <p>
   *   Will return the vanilla block's inventory if it's not a special chest.
   * </p>
   *
   * @param block The block we want to check
   * @param player The player who wants to open the chest
   * @return The inventory the player would see with the given special chest. <code>null</code> if it's not a special chest
   * @see #getChestType(Block)
   */
  @Nullable
  Inventory getChestInventory(Block block, Player player);

  /**
   * Get the folder in which the arena's data is stored.
   *
   * @return The folder in which the arena's data is stored. <code>null</code> if it's a cloned arena
   */
  @Nullable
  File getDataFolder();

  /**
   * Get the file that contains the regeneration data of this arena.
   * <p>
   *   It might not have one if it's cloned or {@link RegenerationType#isNormal()}
   *   returns <code>false</code>.
   * </p>
   *
   * @return The file that contains the regeneration data of this arena. <code>null</code> if it doesn't have one
   */
  @Nullable
  File getDataRegenFile();

  /**
   * Get all the active special item sessions.
   * <p>
   *   A special item might stay active longer. For instance,
   *   with fireballs they need to fly to their target first.
   *   Once they hit something or despawn, the session will be stopped.
   * </p>
   *
   * @return All the active special item sessions
   */
  Collection<SpecialItemUseSession> getSpecialItemSessions();

  /**
   * Returns the {@link RemoteArena} variant of this arena.
   *
   * @return A (local) remote arena that represents this arena
   * @see RemoteAPI#get()
   */
  RemoteArena asRemote();
}
