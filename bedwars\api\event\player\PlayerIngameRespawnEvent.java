package de.marcely.bedwars.api.event.player;

import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.event.arena.ArenaEvent;
import de.marcely.bedwars.tools.Validate;
import lombok.Getter;
import org.bukkit.Location;
import org.bukkit.entity.Player;
import org.bukkit.event.HandlerList;
import org.bukkit.event.player.PlayerEvent;

/**
 * Gets called when a player is about to be respawned during a match
 * <p>
 *   This event gets called right before the player's respawning is being processed,
 *   in which he will receive all the items, be teleported, etc.
 * </p>
 * <p>
 *   This refers to the post-death spectator (5 seconds wait) respawn,
 *   not strictly the death respawn.
 * </p>
 *
 * @see PlayerIngamePostRespawnEvent
 */
public class PlayerIngameRespawnEvent extends PlayerEvent implements ArenaEvent {

  private static final HandlerList HANDLERS = new HandlerList();

  @Getter
  private final Arena arena;
  private boolean givingItems;
  private boolean givingEffects;
  private Location spawnLocation;

  public PlayerIngameRespawnEvent(Player player, Arena arena, boolean givingItems, boolean givingEffects, Location spawnLocation) {
    super(player);

    this.givingItems = givingItems;
    this.givingEffects = givingEffects;
    this.arena = arena;
    this.spawnLocation = spawnLocation;
  }

  /**
   * Returns whether spawn items will be given to the player respawning.
   *
   * @return whether spawn items will be given to the player
   */
  public boolean isGivingItems() {
    return givingItems;
  }

  /**
   * Returns whether effects will be given to the player respawning.
   * <p>
   *   This only references effects related to the <code>giveeffects-on-respawn</code> config.
   * </p>
   *
   * @return whether ffects will be given to the player
   */
  public boolean isGivingEffects() {
    return givingEffects;
  }

  /**
   * Set whether or not spawn items will be given to the player when respawning.
   *
   * @param givingItems whether or not spawn items will be given to the player
   */
  public void setGivingItems(boolean givingItems) {
    this.givingItems = givingItems;
  }

  /**
   * Set whether or not effects will be given to the player when respawning.
   *
   * @param givingEffects whether or not effects will be given to the player
   */
  public void setGivingEffects(boolean givingEffects) {
    this.givingEffects = givingEffects;
  }

  /**
   * Get the location where the player will respawn.
   * <p>
   *   The returned instance is mutable and changing its values will change the respawn location,
   *   even without calling {@link #setSpawnLocation(Location)}.
   * </p>
   *
   * @return the location where the player will respawn
   */
  public Location getSpawnLocation() {
    return this.spawnLocation;
  }

  /**
   * Set the location where the player will respawn.
   *
   * @param spawnLocation the location where the player will respawn
   */
  public void setSpawnLocation(Location spawnLocation) {
    Validate.notNull(spawnLocation, "spawnLocation");

    this.spawnLocation = spawnLocation;
  }


  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }
}

