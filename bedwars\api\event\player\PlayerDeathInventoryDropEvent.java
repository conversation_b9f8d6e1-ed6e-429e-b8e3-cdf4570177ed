package de.marcely.bedwars.api.event.player;

import de.marcely.bedwars.api.BedwarsAPI;
import de.marcely.bedwars.api.GameAPI;
import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.event.arena.ArenaEvent;
import de.marcely.bedwars.api.unsafe.BedwarsAPILayer;
import de.marcely.bedwars.tools.Validate;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.Getter;
import org.bukkit.entity.Player;
import org.bukkit.event.HandlerList;
import org.bukkit.event.player.PlayerEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.plugin.Plugin;
import org.jetbrains.annotations.Nullable;

/**
 * Gets called whenever MBedwars is handling the drops of a players' death.
 * <p>
 *  The determination of what shall be dropped works by a queue that's effectively only a {@link List} under the hood.
 *  You're able to modify the queue within this event. After the execution of this event MBedwars will execute
 *  each {@link Handler} inside the queue which runs in an order from top to bottom. Updated states get passed to the {@link Handler} below.
 *  MBedwars delivers a few default Handlers out of the box, but it depends on the configuration of the user which of them get added to the queue.
 * </p>
 */
@SuppressWarnings("deprecation")
public class PlayerDeathInventoryDropEvent extends PlayerEvent implements ArenaEvent {

  private static final HandlerList HANDLERS = new HandlerList();

  @Getter
  private final Arena arena;

  private final List<Handler> handlerQueue = new ArrayList<>(4);

  public PlayerDeathInventoryDropEvent(Player player, Arena arena) {
    super(player);

    this.arena = arena;
  }

  /**
   * Returns the queue invoked in this event.
   * <p>
   * Entries at index 0 are on top, those at {@link Collection#size()}-1 are at bottom.
   * It's safe to modify this List (add/remove entries).
   *
   * @return The queue involved in this event
   */
  public List<Handler> getHandlerQueue() {
    return this.handlerQueue;
  }

  /**
   * Adds a new Handler to the top of the queue.
   * <p>
   * This Handler might get executed first in the queue and might even contain all original states.
   * Executing this again will put the existing ones below the new one.
   *
   * @param handler Handles what shall happen with the drops
   * @return <code>false</code> if the <code>handler</code> is already included
   */
  public boolean addHandlerToTop(Handler handler) {
    Validate.notNull(handler, "handler");
    Validate.notNull(handler.getPlugin(), "getPlugin() of handler");

    if (this.handlerQueue.contains(handler))
      return false;

    this.handlerQueue.add(0, handler);

    return true;
  }

  /**
   * Adds a new Handler to the bottom of the queue.
   * <p>
   * This Handler might get executed as last in the queue whereby it probably won't have the original states.
   * Executing this again will put the existing ones above the new one.
   *
   * @param handler Handles what shall happen with the drops
   * @return <code>false</code> if the <code>handler</code> is already included
   */
  public boolean addHandlerToBottom(Handler handler) {
    Validate.notNull(handler, "handler");
    Validate.notNull(handler.getPlugin(), "getPlugin() of handler");

    if (this.handlerQueue.contains(handler))
      return false;

    this.handlerQueue.add(handler);

    return true;
  }

  /**
   * Tries to remove a Handler that has been added before
   *
   * @param handler The Handler instance that shall be removed
   * @return <code>true</code> if this queue contained the specified element
   */
  public boolean removeHandler(Handler handler) {
    return this.handlerQueue.remove(handler);
  }


  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }


  public interface Handler {

    Handler DEFAULT_KEEP_SPAWNERS = new PlayerDeathInventoryDropEvent.DefaultHandler() {
      public void execute(Player player, Arena arena, Player killer, List<ItemStack> droppedItems, AtomicInteger droppedExp) {
        final Iterator<ItemStack> it = droppedItems.iterator();

        while (it.hasNext()) {
          if (GameAPI.get().getDropTypeByDrop(it.next()) == null)
            it.remove();
        }
      }
    };

    Handler DEFAULT_REMOVE_ALL = new PlayerDeathInventoryDropEvent.DefaultHandler() {
      public void execute(Player player, Arena arena, Player killer, List<ItemStack> droppedItems, AtomicInteger droppedExp) {
        droppedItems.clear();
        droppedExp.set(0);
      }
    };

    Handler DEFAULT_AUTO_PICKUP = new PlayerDeathInventoryDropEvent.DefaultHandler() {
      public void execute(Player player, Arena arena, Player killer, List<ItemStack> droppedItems, AtomicInteger droppedExp) {
        try {
          if (killer == null ||
              droppedItems.isEmpty() && droppedExp.get() == 0 ||
              !arena.isPlaying(killer) ||
              GameAPI.get().isSpectator(killer))
            return;

          final Collection<ItemStack> remaining =
              killer.getInventory().addItem(droppedItems.toArray(new ItemStack[0])).values();

          killer.setTotalExperience(killer.getTotalExperience()+droppedExp.get());

          if (droppedItems.size() - remaining.size() >= 1)
            BedwarsAPILayer.INSTANCE.playSound(killer, "PICKUP_ITEM");
        } finally {
          droppedItems.clear();
          droppedExp.set(0);
        }
      }
    };

    Plugin getPlugin();

    void execute(Player player, Arena arena, @Nullable Player killer, List<ItemStack> droppedItems, AtomicInteger droppedExp);
  }

  private static abstract class DefaultHandler implements Handler {

    @Override
    public Plugin getPlugin() {
      return BedwarsAPI.getPlugin();
    }
  }
}
