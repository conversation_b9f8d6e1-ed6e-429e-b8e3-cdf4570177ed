package de.marcely.bedwars.api.event.player;

import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.event.arena.ArenaEvent;
import lombok.Getter;
import org.bukkit.entity.Player;
import org.bukkit.event.HandlerList;
import org.bukkit.event.player.PlayerEvent;

/**
 * Gets called after a player has respawned during a match
 * <p>
 *   This event gets called right when the player has fullfilled the respawn-process,
 *   where he received all the items, has been teleported, etc. and is now able to
 *   play.
 * </p>
 * <p>
 *   This refers to the post-death spectator (5 seconds wait) respawn,
 *   not strictly the death respawn.
 * </p>
 *
 * @see PlayerIngameRespawnEvent
 */
public class PlayerIngamePostRespawnEvent extends PlayerEvent implements ArenaEvent {

  private static final HandlerList HANDLERS = new HandlerList();

  @Getter
  private final Arena arena;

  public PlayerIngamePostRespawnEvent(Player player, Arena arena) {
    super(player);

    this.arena = arena;
  }


  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }
}
