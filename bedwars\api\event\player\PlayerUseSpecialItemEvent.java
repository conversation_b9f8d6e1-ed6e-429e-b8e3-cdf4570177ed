package de.marcely.bedwars.api.event.player;

import de.marcely.bedwars.api.arena.Arena;
import de.marcely.bedwars.api.event.arena.ArenaEvent;
import de.marcely.bedwars.api.game.specialitem.SpecialItem;
import de.marcely.bedwars.api.game.specialitem.SpecialItemUseSession;
import de.marcely.bedwars.tools.Validate;
import java.util.ArrayList;
import java.util.Collection;
import java.util.function.Consumer;
import lombok.Getter;
import lombok.Setter;
import org.bukkit.block.Block;
import org.bukkit.block.BlockFace;
import org.bukkit.entity.Player;
import org.bukkit.event.Cancellable;
import org.bukkit.event.HandlerList;
import org.bukkit.event.player.PlayerEvent;
import org.bukkit.inventory.ItemStack;
import org.jetbrains.annotations.Nullable;

/**
 * Gets called when a player is using a special item while he's in a game
 */
public class PlayerUseSpecialItemEvent extends PlayerEvent implements ArenaEvent, Cancellable {

  private static final HandlerList HANDLERS = new HandlerList();

  private final SpecialItem specialItem;
  private final Arena arena;
  private final ItemStack item;
  private final boolean isOffHand;
  private final Block clickedBlock;
  private final BlockFace clickedBlockFace;

  @Getter @Setter
  private boolean cancelled = false;
  private boolean takeItem, naturalInteractionCancelled = true;

  private final Collection<Consumer<SpecialItemUseSession>> sessionListeners = new ArrayList<>(0);

  public PlayerUseSpecialItemEvent(Player player, SpecialItem specialItem, Arena arena, ItemStack item, boolean isOffHand,
      @Nullable Block clickedBlock, @Nullable BlockFace clickedBlockFace, boolean takeItem) {
    super(player);

    this.specialItem = specialItem;
    this.arena = arena;
    this.item = item;
    this.isOffHand = isOffHand;
    this.clickedBlock = clickedBlock;
    this.clickedBlockFace = clickedBlockFace;
    this.takeItem = takeItem;
  }

  /**
   * Returns the special item that the player has used.
   *
   * @return The special item that has been used
   */
  public SpecialItem getSpecialItem() {
    return this.specialItem;
  }

  /**
   * Returns the arena in which the player has used the item.
   *
   * @return The arena involved in this
   */
  public Arena getArena() {
    return this.arena;
  }

  /**
   * Returns the item with which the player interacted.
   *
   * @return The item that the player right-clicked
   */
  public ItemStack getItem() {
    return this.item;
  }

  /**
   * Returns whether the player used the item in his off-hand.
   * <p>
   * This method always returns false on 1.8.8, as off-hand got added with 1.9.
   * </p>
   *
   * @return If the player used the item in his off-hand
   */
  public boolean isOffHand() {
    return this.isOffHand;
  }

  /**
   * Returns the block on which the player clicked.
   * <p>
   * Can be <code>null</code> when the player used the item while looking to the sky or at an hologram.
   *
   * @return The block that has been clicked
   */
  public @Nullable Block getClickedBlock() {
    return this.clickedBlock;
  }

  /**
   * Returns the block face on which the player clicked.
   * <p>
   * Can be <code>null</code> when the player used the item while looking to the sky or at an hologram.
   *
   * @return The block face on which he clicked
   */
  public @Nullable BlockFace getClickedBlockFace() {
    return this.clickedBlockFace;
  }

  /**
   * Returns whether the item will be taken from the players inventory.
   *
   * @return If the item will be taken away
   */
  public boolean isTakingItem() {
    return this.takeItem;
  }

  /**
   * Set whether the item should be taken away from the player.
   *
   * @param takeItem If the item shall be taken away
   */
  public void setTakingItem(boolean takeItem) {
    this.takeItem = takeItem;
  }

  /**
   * Returns whether the plugin will accept the handling of the natural behaviour of whatever would actually happen.
   * <p>
   * For instance, when holding a block, the player would (possibly) place the block. By default, the plugin returns <code>true</code> unless it has been changed using
   * {@link #setNaturalInteractionCancelled(boolean)}.
   * </p>
   *
   * @return If the natural behaviour of the interaction will be processed
   */
  public boolean isNaturalInteractionCancelled() {
    return this.naturalInteractionCancelled;
  }

  /**
   * Set whether the plugin should accept the handling of the natural behaviour of whatever would actually happen.
   * <p>
   * For instance, when holding a block, the player would (possibly) place the block. By default, the plugin has set it to <code>true</code>.
   * </p>
   *
   * @param newState If the natural behaviour of the interaction shall be processed
   */
  public void setNaturalInteractionCancelled(boolean newState) {
    this.naturalInteractionCancelled = newState;
  }

  /**
   * Returns the inventory slot in which the item is located.
   *
   * @return The inventory slot of the item
   */
  public int getInventorySlot() {
    return this.isOffHand ? 40 : this.player.getInventory().getHeldItemSlot();
  }

  /**
   * Adds a listener that gets called once the session has been initiated.
   * <p>
   *   The initiation occurs immediately after processing this event-loop.
   *   It might not occur at all if this event is cancelled or an error occurs
   *   during initiation.
   * </p>
   *
   * @param callback The callback that gets called once the session has been initiated
   */
  public void addSessionListener(Consumer<SpecialItemUseSession> callback) {
    Validate.notNull(callback, "callback");

    this.sessionListeners.add(callback);
  }

  /**
   * Returns the listeners that will be called once the session has been initiated.
   *
   * @return The listeners that will be called once the session has been initiated
   * @see #addSessionListener(Consumer)
   */
  public Collection<Consumer<SpecialItemUseSession>> getSessionListeners() {
    return this.sessionListeners;
  }

  @Override
  public HandlerList getHandlers() {
    return HANDLERS;
  }

  public static HandlerList getHandlerList() {
    return HANDLERS;
  }
}
